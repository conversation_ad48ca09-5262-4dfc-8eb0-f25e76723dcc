feat: add automatic sorting to order summary table

- Sort order summary table by total quantity (Tổng) in descending order
- Add secondary sort by material name (<PERSON><PERSON><PERSON>) alphabetically
- Add visual indicator (arrow down icon) next to "Tổng" column header
- Include tooltip explaining sorting behavior
- Update subtitle to show "Sắp xếp theo tổng số lượng"

Sorting logic:
- Primary: Total quantity (highest to lowest)
- Secondary: Material name (A to Z) for items with same total
- Performance: O(n log n) efficient sorting algorithm

Benefits:
- High-volume materials appear at top for better visibility
- Easier identification of priority items for procurement
- Consistent ordering with predictable secondary sort
- Improved user experience for data analysis

Technical implementation:
```javascript
items.sort((a, b) => {
  const totalDiff = b['Tổng'] - a['Tổng'];
  if (totalDiff !== 0) return totalDiff;
  return a['Tên Vật Tư'].localeCompare(b['Tên Vậ<PERSON> Tư']);
});
```

Build size: 961.28 kB JS bundle
