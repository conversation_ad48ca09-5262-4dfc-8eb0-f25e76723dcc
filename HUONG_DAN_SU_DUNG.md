# Hướng Dẫn Sử Dụng - Ứng Dụng Xác Thực Hóa Đơn Thuế Việt Nam

## 📖 Hướng dẫn chi tiết

👉 **[Xem hướng dẫn đầy đủ tại đây](https://gist.github.com/quang21122/607b48839cd27a15782966747ff397dd)**

## <PERSON><PERSON><PERSON>

1. [Giớ<PERSON> Thiệu Tổng <PERSON>uan](#giới-thiệu-tổng-quan)
2. [C<PERSON>u Hình <PERSON>hu<PERSON> Suất](#cấu-hình-thuế-suất)
3. [Logic AND/OR Trong Quy Tắc](#logic-andor-trong-quy-tắc)
4. [<PERSON><PERSON> Tác File JSON/XML](#thao-tác-file-jsonxml)
5. [T<PERSON><PERSON>/Xuất Cấu <PERSON>](#tải-lênxuất-cấu-hình)
6. [<PERSON><PERSON><PERSON>h<PERSON>c Thông Tin Người Mu<PERSON>](#xác-thực-thông-tin-người-mua)
7. [<PERSON><PERSON>ớng Dẫn Giao <PERSON>](#hướng-dẫn-giao-diện)
8. [X<PERSON> Lý Lỗi và Khắc Phục](#xử-lý-lỗi-và-khắc-phục)
9. [Câu Hỏi Thường Gặp](#câu-hỏi-thường-gặp)

---

## Giới Thiệu Tổng Quan

Ứng dụng **Xác Thực Hóa Đơn Thuế Việt Nam** là công cụ chuyên dụng để kiểm tra và xác thực các hóa đơn điện tử theo quy định của Việt Nam. Ứng dụng hỗ trợ:

### Tính Năng Chính

- ✅ **Xác thực thông tin người mua**: Mã số thuế (MST), tên công ty, địa chỉ
- ✅ **Kiểm tra thuế suất**: Xác thực thuế suất theo quy tắc cấu hình
- ✅ **Xử lý file XML/JSON**: Hỗ trợ tải lên file đơn lẻ hoặc thư mục
- ✅ **Cấu hình linh hoạt**: Tạo và quản lý quy tắc xác thực tùy chỉnh
- ✅ **Xuất/nhập cấu hình**: Sao lưu và khôi phục cài đặt

### Định Dạng File Hỗ Trợ

- **XML**: File hóa đơn điện tử gốc
- **JSON**: Dữ liệu đã được chuyển đổi từ XML

### Cấu Trúc Dữ Liệu Hóa Đơn

Ứng dụng làm việc với cấu trúc hóa đơn điện tử Việt Nam:

```
HDon
├── DLHDon
│   ├── TTChung (Thông tin chung)
│   ├── NDHDon
│   │   ├── NBan (Người bán)
│   │   ├── NMua (Người mua)
│   │   └── DSHHDVu (Danh sách hàng hóa dịch vụ)
│   │       └── HHDVu[] (Các mặt hàng)
│   └── TToan (Thông tin tổng)
```

---

## Cấu Hình Thuế Suất

### Khái Niệm Cơ Bản

**Cấu hình thuế suất** cho phép bạn thiết lập các quy tắc để kiểm tra tính chính xác của thuế suất trên từng mặt hàng trong hóa đơn.

### Cách Truy Cập

1. Mở ứng dụng
2. Tìm phần **"Cấu hình thuế suất"** ở bên phải màn hình
3. Phần này hiển thị các quy tắc hiện tại và cho phép thêm mới

### Tạo Quy Tắc Thuế Suất Mới

#### Bước 1: Thêm Quy Tắc

1. Nhấn nút **"Thêm quy tắc"** (biểu tượng ➕)
2. Điền thông tin cơ bản:
   - **Tên quy tắc**: Tên mô tả (VD: "Thuế suất thực phẩm")
   - **Mô tả**: Giải thích chi tiết (tùy chọn)

#### Bước 2: Thiết Lập Điều Kiện

Mỗi quy tắc có thể có nhiều điều kiện. Các điều kiện trong cùng một quy tắc sử dụng logic **AND** (tất cả phải đúng).

**Các trường điều kiện phổ biến:**

- `NBan.Ten`: Tên người bán
- `NBan.MST`: Mã số thuế người bán
- `NBan.DChi`: Địa chỉ người bán
- `THHDVu`: Tên hàng hóa/dịch vụ
- `DVTinh`: Đơn vị tính
- `TTChung.SHDon`: Số hóa đơn

#### Bước 3: Thiết Lập Thuế Suất Mong Đợi

Cho mỗi điều kiện, chọn thuế suất mong đợi:

- **0%**: Không chịu thuế
- **5%**: Thuế suất ưu đãi
- **8%**: Thuế suất đặc biệt
- **10%**: Thuế suất tiêu chuẩn
- **KT**: Không tính thuế

---

## Logic AND/OR Trong Quy Tắc

### 🆕 Tính Năng Mới: Lựa Chọn Logic Operator

Từ phiên bản mới, bạn có thể chọn cách kết hợp các điều kiện trong một quy tắc:

#### Cách Thiết Lập Logic Operator

1. Khi tạo quy tắc mới, tìm phần **"Cách kết hợp các điều kiện"**
2. Chọn một trong hai tùy chọn:
   - **AND (Tất cả điều kiện phải đúng)**
   - **OR (Chỉ cần một điều kiện đúng)**

### Logic AND - Tất Cả Điều Kiện Phải Đúng

**Khi sử dụng:** Khi bạn muốn áp dụng quy tắc chỉ khi TẤT CẢ điều kiện được thỏa mãn.

**Ví dụ:**

```
Quy tắc: "Đường từ công ty ABC"
├── Logic: AND
├── Điều kiện 1: THHDVu chứa "đường" → Thuế suất: 5%
└── Điều kiện 2: NBan.Ten chứa "ABC" → Thuế suất: 5%

→ Chỉ áp dụng khi CÙNG LÚC:
  • Tên hàng chứa "đường" VÀ
  • Tên người bán chứa "ABC"
```

**Lưu ý:** Với logic AND, tất cả điều kiện phải có cùng thuế suất.

### Logic OR - Chỉ Cần Một Điều Kiện Đúng

**Khi sử dụng:** Khi bạn muốn áp dụng quy tắc khi BẤT KỲ điều kiện nào được thỏa mãn.

**Ví dụ:**

```
Quy tắc: "Thuế suất đặc biệt"
├── Logic: OR
├── Điều kiện 1: THHDVu chứa "đường" → Thuế suất: 5%
└── Điều kiện 2: THHDVu chứa "ống hút" → Thuế suất: 10%

→ Áp dụng khi:
  • Tên hàng chứa "đường" → 5% HOẶC
  • Tên hàng chứa "ống hút" → 10%
```

**Lưu ý:** Với logic OR, mỗi điều kiện có thể có thuế suất khác nhau.

### Hiển Thị Trong Giao Diện

#### Connector Giữa Điều Kiện

- **Logic AND**: Hiển thị "và" giữa các điều kiện
- **Logic OR**: Hiển thị "hoặc" giữa các điều kiện

#### Thông Tin Quy Tắc

Trong danh sách quy tắc, bạn sẽ thấy:

- **"(Tất cả phải đúng)"** cho logic AND
- **"(Chỉ cần một điều kiện đúng)"** cho logic OR

### Ví Dụ Thực Tế

#### Trường Hợp 1: Kiểm Tra Nghiêm Ngặt (AND)

```
Quy tắc: "Thực phẩm từ nhà cung cấp tin cậy"
├── Logic: AND
├── THHDVu chứa "thực phẩm" → 5%
├── NBan.Ten chứa "ORGANIC" → 5%
└── NBan.DChi chứa "TP.HCM" → 5%

→ Chỉ áp dụng 5% khi tất cả 3 điều kiện đều đúng
```

#### Trường Hợp 2: Linh Hoạt Nhiều Lựa Chọn (OR)

```
Quy tắc: "Các mặt hàng ưu đãi"
├── Logic: OR
├── THHDVu chứa "sách" → 0%
├── THHDVu chứa "thuốc" → 5%
└── THHDVu chứa "thực phẩm" → 5%

→ Áp dụng thuế suất tương ứng cho bất kỳ mặt hàng nào khớp
```

### Migration Tự Động

- Tất cả quy tắc cũ sẽ tự động được chuyển đổi với logic **AND**
- Không ảnh hưởng đến dữ liệu hiện có
- Đảm bảo tương thích ngược hoàn toàn

### Sử Dụng Pattern Matching với Dấu Sao (\*)

Ứng dụng hỗ trợ pattern matching linh hoạt với ký tự `*`:

#### Các Loại Pattern

1. **`*text`**: Kết thúc bằng "text"

   - VD: `*đường` khớp với "bột đường", "cát đường"

2. **`text*`**: Bắt đầu bằng "text"

   - VD: `đường*` khớp với "đường cát", "đường phèn"

3. **`*text*`**: Chứa "text"

   - VD: `*đường*` khớp với "bột đường cát", "xi-rô đường"

4. **`*`**: Khớp với mọi giá trị
   - Sử dụng khi muốn áp dụng cho tất cả

#### Ví Dụ Thực Tế

```
Điều kiện: THHDVu = "*ống hút*"
→ Khớp với: "Ống hút nhựa", "Bộ ống hút inox", "Ống hút giấy sinh học"

Điều kiện: NBan.Ten = "CÔNG TY*"
→ Khớp với: "CÔNG TY TNHH ABC", "CÔNG TY CỔ PHẦN XYZ"
```

### Thêm Trường Tùy Chỉnh

#### Sử dụng Trường Có Sẵn

Chọn từ danh sách các trường được định nghĩa sẵn trong dropdown.

#### Tạo Trường Tùy Chỉnh

1. Nhấn **"Thêm trường tùy chỉnh"**
2. Chọn **"Tạo trường mới"**
3. Nhập đường dẫn JSON (VD: `TTChung.NLap` cho ngày lập)
4. Nhập nhãn hiển thị (VD: "Ngày lập hóa đơn")

**Lưu ý về đường dẫn JSON:**

- Sử dụng dấu chấm (.) để phân tách các cấp
- Phải khớp chính xác với cấu trúc JSON của hóa đơn
- VD: `HDon.DLHDon.TTChung.SHDon` cho số hóa đơn

### Ví Dụ Cấu Hình Hoàn Chỉnh

```
Quy tắc 1: "Thuế suất thực phẩm cơ bản"
├── Điều kiện 1: THHDVu = "*gạo*" → Thuế suất: 5%
├── Điều kiện 2: NBan.Ten = "*THỰC PHẨM*" → Thuế suất: 5%
└── Mô tả: "Áp dụng cho gạo từ các công ty thực phẩm"

Quy tắc 2: "Thuế suất đồ nhựa"
├── Điều kiện 1: THHDVu = "*nhựa*" → Thuế suất: 10%
└── Mô tả: "Thuế suất tiêu chuẩn cho sản phẩm nhựa"
```

---

## Thao Tác File JSON/XML

### Các Cách Tải File

#### 1. Kéo Thả (Drag & Drop)

- **Thư mục**: Kéo thả thư mục chứa file XML vào vùng drop zone
- **File đơn lẻ**: Kéo thả file XML trực tiếp

#### 2. Chọn File Thủ Công

- **Nút "Chọn thư mục"**: Duyệt và chọn thư mục
- **Nút "Chọn file XML"**: Chọn file XML đơn lẻ

### Định Dạng File Được Hỗ Trợ

#### File XML

- **Phần mở rộng**: `.xml`
- **Mã hóa**: UTF-8 (khuyến nghị)
- **Cấu trúc**: Tuân thủ định dạng hóa đơn điện tử Việt Nam

#### Cấu Trúc XML Mẫu

```xml
<?xml version="1.0" encoding="UTF-8"?>
<HDon>
  <DLHDon>
    <TTChung>
      <SHDon>0000001</SHDon>
      <NLap>2024-01-15</NLap>
    </TTChung>
    <NDHDon>
      <NBan>
        <Ten>CÔNG TY CỔ PHẦN TTMI</Ten>
        <MST>0317025010</MST>
        <DChi>146 Bình Lợi, P.13, Q.Bình Thạnh, TP.HCM</DChi>
      </NBan>
      <NMua>
        <Ten>CÔNG TY ABC</Ten>
        <MST>0123456789</MST>
        <DChi>123 Đường XYZ, P.1, Q.1, TP.HCM</DChi>
      </NMua>
      <DSHHDVu>
        <HHDVu>
          <THHDVu>Đường cát trắng</THHDVu>
          <DVTinh>Kg</DVTinh>
          <TSuat>5</TSuat>
        </HHDVu>
      </DSHHDVu>
    </NDHDon>
  </DLHDon>
</HDon>
```

### Xử Lý File và Hiển Thị

#### Sau Khi Tải File

1. **Danh sách file**: Hiển thị tất cả file đã tải
2. **Trạng thái xử lý**: Biểu tượng cho biết file đã được xử lý
3. **Kết quả xác thực**: Màu sắc thể hiện kết quả (xanh = hợp lệ, đỏ = lỗi)

#### Xem Chi Tiết File

1. **Nhấn vào file** trong danh sách
2. **Tab "Kết quả kiểm tra"**: Xem kết quả xác thực
3. **Tab "Dữ liệu JSON"**: Xem dữ liệu đã chuyển đổi

### Yêu Cầu Kỹ Thuật

#### Dung Lượng File

- **File đơn lẻ**: Tối đa 10MB
- **Thư mục**: Không giới hạn số lượng file

#### Trình Duyệt Hỗ Trợ

- **Chrome**: Phiên bản 86+
- **Firefox**: Phiên bản 82+
- **Edge**: Phiên bản 86+
- **Safari**: Phiên bản 14+

#### Lưu Ý Bảo Mật

- File được xử lý hoàn toàn trên trình duyệt
- Không có dữ liệu được gửi lên server
- Dữ liệu tự động xóa khi đóng trình duyệt

---

## Tải Lên/Xuất Cấu Hình

### Xuất Cấu Hình (Export)

#### Mục Đích

- Sao lưu cấu hình hiện tại
- Chia sẻ cấu hình với người khác
- Tạo bản backup trước khi thay đổi

#### Cách Thực Hiện

1. Trong phần **"Cấu hình thuế suất"**
2. Nhấn nút **"Xuất cấu hình"** (biểu tượng 📥)
3. File sẽ được tải xuống với tên: `cau-hinh-thue-suat-YYYY-MM-DD.json`

#### Nội Dung File Xuất

```json
{
  "rules": [
    {
      "id": "food-items",
      "name": "Thuế suất cho thực phẩm",
      "conditions": [
        {
          "field": "THHDVu",
          "operator": "contains",
          "value": "đường",
          "taxRate": 5
        }
      ],
      "description": "Thuế suất đặc biệt cho các mặt hàng thực phẩm"
    }
  ],
  "enabled": true,
  "lastUpdated": "2024-01-15T10:30:00.000Z"
}
```

### Nhập Cấu Hình (Import)

#### Cách Thực Hiện

1. Nhấn nút **"Nhập cấu hình"** (biểu tượng 📤)
2. Chọn file JSON cấu hình đã xuất trước đó
3. Hệ thống sẽ kiểm tra tính hợp lệ của file
4. Nếu hợp lệ, cấu hình mới sẽ thay thế cấu hình hiện tại

#### Xử Lý Lỗi Nhập

- **File không hợp lệ**: Hiển thị thông báo lỗi chi tiết
- **Cấu trúc sai**: Hướng dẫn sửa lỗi
- **Phiên bản cũ**: Tự động chuyển đổi định dạng

#### Lưu Ý Quan Trọng

⚠️ **Cảnh báo**: Nhập cấu hình sẽ **ghi đè hoàn toàn** cấu hình hiện tại. Hãy xuất backup trước khi nhập.

### Khôi Phục Cấu Hình Mặc Định

#### Khi Nào Sử Dụng

- Cấu hình bị lỗi không khôi phục được
- Muốn bắt đầu lại từ đầu
- Cấu hình quá phức tạp, cần đơn giản hóa

#### Cách Thực Hiện

1. Nhấn nút **"Khôi phục mặc định"**
2. Xác nhận trong hộp thoại cảnh báo
3. Hệ thống sẽ khôi phục về cấu hình ban đầu

#### Cấu Hình Mặc Định Bao Gồm

- Quy tắc thuế suất cho thực phẩm (5%)
- Quy tắc thuế suất cho đồ nhựa (10%)
- Các trường điều kiện cơ bản

### Lưu Trữ Cấu Hình

#### Cơ Chế Lưu Trữ

- **LocalStorage**: Cấu hình được lưu trực tiếp trên trình duyệt
- **Tự động lưu**: Mọi thay đổi được lưu ngay lập tức
- **Bảo mật**: Dữ liệu chỉ tồn tại trên máy tính của bạn

#### Quản Lý Dữ Liệu

- **Xóa dữ liệu**: Xóa cache trình duyệt sẽ mất cấu hình
- **Sao lưu định kỳ**: Khuyến nghị xuất cấu hình thường xuyên
- **Đồng bộ**: Không hỗ trợ đồng bộ giữa các thiết bị

---

## Xác Thực Thông Tin Người Mua

### Tổng Quan

Tính năng này kiểm tra thông tin người mua (NMua) trong hóa đơn với cơ sở dữ liệu tham chiếu để đảm bảo tính chính xác.

### Các Trường Được Kiểm Tra

#### 1. Mã Số Thuế (MST)

- **Mục đích**: Xác thực tính hợp lệ của mã số thuế
- **Cách kiểm tra**: So sánh chính xác với database
- **Kết quả**: Hợp lệ/Không tìm thấy

#### 2. Tên Công Ty (Ten)

- **Mục đích**: Kiểm tra tên công ty khớp với MST
- **Cách kiểm tra**: So sánh chuỗi (không phân biệt hoa thường)
- **Xử lý**: Loại bỏ khoảng trắng thừa

#### 3. Địa Chỉ (DChi)

- **Mục đích**: Xác thực địa chỉ công ty
- **Đặc biệt**: Hỗ trợ viết tắt địa chỉ Việt Nam
- **Logic**: So sánh thông minh với nhiều định dạng

### Hỗ Trợ Viết Tắt Địa Chỉ Việt Nam

#### Viết Tắt Địa Phương

| Đầy Đủ    | Viết Tắt | Ví Dụ             |
| --------- | -------- | ----------------- |
| Phường    | P        | P.13, Phường 13   |
| Quận      | Q        | Q.1, Quận 1       |
| Thị trấn  | TT       | TT.Hóc Môn        |
| Thị xã    | TX       | TX.Thuận An       |
| Thành phố | TP       | TP.HCM, TP.Hà Nội |
| Việt Nam  | VN       | VN, Việt Nam      |

#### Viết Tắt Doanh Nghiệp

| Đầy Đủ               | Viết Tắt |
| -------------------- | -------- |
| Trách nhiệm hữu hạn  | TNHH     |
| Doanh nghiệp tư nhân | DNTN     |
| Hợp danh             | HD       |
| Khu công nghiệp      | KCN      |
| Khu chế xuất         | KCX      |
| Khu công nghệ cao    | KCNC     |
| Sản xuất             | SX       |
| Chi nhánh            | CN       |
| Văn phòng đại diện   | VPĐD     |

#### Ví Dụ So Sánh Địa Chỉ

```
Địa chỉ trong hóa đơn: "146 Bình Lợi, P.13, Q.Bình Thạnh, TP.HCM, VN"
Địa chỉ tham chiếu: "146 Bình Lợi, Phường 13, Quận Bình Thạnh, Thành phố Hồ Chí Minh, Việt Nam"

→ Kết quả: KHỚP (nhờ hỗ trợ viết tắt)
```

### Chọn Công Ty Để Kiểm Tra

#### Dropdown Chọn Công Ty

1. **Vị trí**: Phía trên danh sách file
2. **Tùy chọn**:
   - "Tất cả công ty": Kiểm tra với toàn bộ database
   - Các công ty cụ thể: Chỉ kiểm tra với công ty được chọn

#### Ảnh Hưởng Của Việc Chọn Công Ty

- **Kết quả xác thực**: Chỉ hiển thị kết quả liên quan đến công ty được chọn
- **Hiệu suất**: Tăng tốc độ xử lý khi chọn công ty cụ thể
- **Báo cáo**: Tập trung vào thông tin cần thiết

### Hiển Thị Kết Quả Xác Thực

#### Thông Tin Tổng Quan

- **Trạng thái tổng thể**: Khớp hoàn toàn/Không tìm thấy
- **Biểu tượng**: ✅ (hợp lệ) hoặc ❌ (lỗi)
- **Màu sắc**: Xanh (thành công), Đỏ (lỗi)

#### Chi Tiết Từng Trường

```
┌─ Thông tin từ hóa đơn ─┐  ┌─ Thông tin tham chiếu ─┐
│ MST: 0317025010        │  │ MST: 0317025010        │
│ Tên: CÔNG TY TTMI      │  │ Tên: CÔNG TY CỔ PHẦN   │
│ Địa chỉ: 146 Bình Lợi  │  │      TTMI              │
└────────────────────────┘  │ Địa chỉ: 146 Bình Lợi, │
                            │          P.13, Q.BT    │
                            └────────────────────────┘
```

#### Kết Quả Kiểm Tra Từng Trường

- **Mã số thuế**: ✅ Hợp lệ
- **Tên công ty**: ❌ Không khớp hoàn toàn
- **Địa chỉ**: ✅ Khớp (có hỗ trợ viết tắt)

### Cơ Sở Dữ Liệu Tham Chiếu

#### Dữ Liệu Mẫu Hiện Tại

```
1. CÔNG TY CỔ PHẦN TTMI
   - MST: 0317025010
   - Địa chỉ: 146 Bình Lợi, Phường 13, Quận Bình Thạnh, TP.HCM

2. CÔNG TY CỔ PHẦN THƯƠNG MẠI ĐẦU TƯ SK
   - MST: 0317749937
   - Địa chỉ: 148 Bình Lợi, Phường 13, Quận Bình Thạnh, TP.HCM

3. CÔNG TY CỔ PHẦN THƯƠNG MẠI - DỊCH VỤ - SẢN XUẤT 3T
   - MST: 0317510698
   - Địa chỉ: 148 Bình Lợi, Phường 13, Quận Bình Thạnh, TP.HCM
```

#### Mở Rộng Dữ Liệu

Trong môi trường thực tế, dữ liệu này sẽ được lấy từ:

- Cơ sở dữ liệu doanh nghiệp
- API của Tổng cục Thuế
- File cấu hình tùy chỉnh
- Hệ thống ERP nội bộ

---

## Hướng Dẫn Giao Diện

### Bố Cục Tổng Thể

#### Màn Hình Chính

Ứng dụng được chia thành 2 phần chính:

- **Bên trái**: Khu vực tải file và hiển thị kết quả
- **Bên phải**: Cấu hình thuế suất

#### Responsive Design

- **Desktop**: Hiển thị 2 cột song song
- **Mobile/Tablet**: Chuyển thành layout dọc, dễ sử dụng trên thiết bị nhỏ

### Khu Vực Tải File (Bên Trái)

#### Trạng Thái Ban Đầu

Khi chưa tải file nào:

- **Drop Zone**: Vùng kéo thả file lớn ở giữa
- **Nút chọn file**: "Chọn thư mục" và "Chọn file XML"
- **Dropdown công ty**: Cho phép chọn công ty để kiểm tra

#### Sau Khi Tải File

- **Danh sách file**: Hiển thị tất cả file đã tải
- **Bảng thông tin**: Tên file, kích thước, trạng thái xử lý
- **Nút "Xóa tất cả"**: Để xóa toàn bộ file và bắt đầu lại

#### Chọn File Để Xem Chi Tiết

1. **Nhấn vào file** trong danh sách
2. **Khu vực chi tiết** xuất hiện bên dưới với 2 tab:
   - **Tab "Kết quả kiểm tra"**: Hiển thị kết quả xác thực
   - **Tab "Dữ liệu JSON"**: Hiển thị dữ liệu thô đã chuyển đổi

### Khu Vực Cấu Hình Thuế Suất (Bên Phải)

#### Header

- **Tiêu đề**: "Cấu hình thuế suất"
- **Trạng thái**: Hiển thị số lượng quy tắc hiện tại

#### Thanh Công Cụ

- **Xuất cấu hình** (📥): Tải file cấu hình về máy
- **Nhập cấu hình** (📤): Tải file cấu hình lên
- **Khôi phục mặc định**: Reset về cài đặt ban đầu

#### Danh Sách Quy Tắc

- **Thẻ quy tắc**: Mỗi quy tắc hiển thị trong một thẻ riêng
- **Thông tin tóm tắt**: Tên, số điều kiện, mô tả
- **Nút xóa**: Để xóa quy tắc (biểu tượng 🗑️)

#### Form Thêm Quy Tắc Mới

- **Nút "Thêm quy tắc"**: Mở form tạo quy tắc mới
- **Form chi tiết**: Tên, mô tả, danh sách điều kiện
- **Nút lưu/hủy**: Để xác nhận hoặc hủy bỏ

### Điều Hướng và Tương Tác

#### Phím Tắt

- **Ctrl + O**: Mở dialog chọn file
- **Ctrl + S**: Xuất cấu hình (nếu có quy tắc)
- **Escape**: Đóng dialog/form đang mở

#### Drag & Drop

- **Kéo file vào drop zone**: Tự động xử lý
- **Kéo thư mục**: Xử lý tất cả file XML trong thư mục
- **Visual feedback**: Thay đổi màu sắc khi kéo file vào

#### Responsive Behavior

- **Màn hình nhỏ**: Ẩn một số label, thu gọn bảng
- **Touch friendly**: Nút và link đủ lớn để chạm dễ dàng
- **Scroll**: Tự động cuộn đến nội dung quan trọng

### Hiển Thị Kết Quả Xác Thực

#### Màu Sắc và Biểu Tượng

- **Xanh lá + ✅**: Xác thực thành công
- **Đỏ + ❌**: Có lỗi xác thực
- **Vàng + ⚠️**: Cảnh báo (thuế suất không khớp)
- **Xám + ℹ️**: Thông tin bổ sung

#### Layout Kết Quả

```
┌─ Kết quả kiểm tra: filename.xml ─────────────────┐
│ ✅ Hợp lệ                                        │
├──────────────────────────────────────────────────┤
│ 📋 Thông tin trích xuất                          │
│ • Số hóa đơn: 0000001                           │
│ • Ngày lập: 2024-01-15                          │
├──────────────────────────────────────────────────┤
│ 👤 Thông tin người mua cần kiểm tra              │
│ ✅ Khớp hoàn toàn                                │
├──────────────────────────────────────────────────┤
│ ⚠️ Kết quả kiểm tra thuế suất                    │
│ • Mặt hàng 1: Đường cát trắng - ❌ Thuế suất 10% │
│   (Mong đợi: 5%)                                │
└──────────────────────────────────────────────────┘
```

#### Thông Báo Lỗi

- **Lỗi file**: Hiển thị chi tiết lỗi phân tích XML
- **Lỗi cấu hình**: Hướng dẫn sửa lỗi cấu hình
- **Lỗi mạng**: Thông báo khi không thể tải dữ liệu

### Accessibility (Khả Năng Tiếp Cận)

#### Hỗ Trợ Screen Reader

- **ARIA labels**: Tất cả thành phần có nhãn mô tả
- **Semantic HTML**: Sử dụng đúng thẻ HTML
- **Focus management**: Quản lý focus khi mở/đóng dialog

#### Keyboard Navigation

- **Tab order**: Thứ tự tab logic và dễ hiểu
- **Enter/Space**: Kích hoạt nút và link
- **Arrow keys**: Điều hướng trong dropdown

#### Contrast và Màu Sắc

- **High contrast**: Đảm bảo tỷ lệ tương phản tốt
- **Color blind friendly**: Không chỉ dựa vào màu sắc
- **Dark mode**: Hỗ trợ chế độ tối (theo hệ thống)

---

## Xử Lý Lỗi và Khắc Phục

### Lỗi Phổ Biến

#### 1. Lỗi File XML

**Triệu chứng:**

- Thông báo "Lỗi phân tích XML"
- File không hiển thị kết quả xác thực

**Nguyên nhân:**

- File XML bị hỏng hoặc không đúng định dạng
- Mã hóa file không phải UTF-8
- Thiếu thẻ đóng XML

**Cách khắc phục:**

1. Kiểm tra file XML bằng trình soạn thảo text
2. Đảm bảo file có header `<?xml version="1.0" encoding="UTF-8"?>`
3. Kiểm tra tất cả thẻ XML đều được đóng đúng cách
4. Sử dụng công cụ validate XML online để kiểm tra

#### 2. Lỗi Cấu Hình Thuế Suất

**Triệu chứng:**

- Không thể lưu quy tắc mới
- Thông báo "Cấu hình không hợp lệ"

**Nguyên nhân:**

- Tên quy tắc để trống
- Điều kiện không có giá trị
- Đường dẫn JSON không đúng định dạng

**Cách khắc phục:**

1. Đảm bảo tên quy tắc không để trống
2. Kiểm tra tất cả điều kiện đều có giá trị
3. Xác thực đường dẫn JSON (VD: `NBan.Ten`, không phải `NBan/Ten`)

#### 3. Lỗi Import Cấu Hình

**Triệu chứng:**

- Thông báo "File cấu hình không hợp lệ"
- Import không thành công

**Nguyên nhân:**

- File JSON bị hỏng
- Cấu trúc file không đúng
- File không phải do ứng dụng xuất ra

**Cách khắc phục:**

1. Chỉ import file được xuất từ ứng dụng này
2. Kiểm tra file JSON có đúng cấu trúc không
3. Thử khôi phục cấu hình mặc định rồi import lại

#### 4. Lỗi Hiệu Suất

**Triệu chứng:**

- Ứng dụng chạy chậm
- Trình duyệt bị treo khi xử lý file lớn

**Nguyên nhân:**

- File XML quá lớn (>10MB)
- Quá nhiều file được tải cùng lúc
- Cấu hình thuế suất quá phức tạp

**Cách khắc phục:**

1. Chia nhỏ file lớn thành nhiều file nhỏ
2. Xử lý từng batch file thay vì tất cả cùng lúc
3. Đơn giản hóa quy tắc thuế suất
4. Đóng các tab trình duyệt khác để giải phóng RAM

### Thông Báo Lỗi Chi Tiết

#### Lỗi Xác Thực Thuế Suất

```
❌ Thuế suất không hợp lệ cho mặt hàng "Đường cát trắng"
   • Thuế suất thực tế: 10%
   • Thuế suất mong đợi: 5%
   • Quy tắc áp dụng: "Thuế suất thực phẩm"
   • Điều kiện không thỏa mãn: Tên hàng phải chứa "đường"
```

#### Lỗi Thông Tin Người Mua

```
❌ Thông tin người mua không khớp
   • Mã số thuế: ✅ Hợp lệ (0317025010)
   • Tên công ty: ❌ Không khớp
     - Từ hóa đơn: "CÔNG TY TTMI"
     - Tham chiếu: "CÔNG TY CỔ PHẦN TTMI"
   • Địa chỉ: ✅ Khớp (có hỗ trợ viết tắt)
```

### Debug và Troubleshooting

#### Kiểm Tra Console

1. Mở Developer Tools (F12)
2. Chuyển sang tab Console
3. Tìm thông báo lỗi màu đỏ
4. Copy thông báo lỗi để báo cáo

#### Kiểm Tra Network

1. Tab Network trong Developer Tools
2. Reload trang và kiểm tra request nào bị lỗi
3. Kiểm tra kết nối internet

#### Xóa Cache và Cookies

1. Ctrl + Shift + Delete
2. Chọn "Cached images and files"
3. Chọn "Cookies and other site data"
4. Nhấn "Clear data"

#### Reset Ứng Dụng

1. Xuất cấu hình hiện tại (backup)
2. Xóa tất cả dữ liệu LocalStorage:
   - F12 → Application → Local Storage
   - Xóa tất cả entries
3. Reload trang
4. Import lại cấu hình nếu cần

### Liên Hệ Hỗ Trợ

#### Thông Tin Cần Cung Cấp

- **Trình duyệt**: Chrome/Firefox/Edge + phiên bản
- **Hệ điều hành**: Windows/Mac/Linux
- **File mẫu**: File XML gây lỗi (nếu có thể chia sẻ)
- **Cấu hình**: File cấu hình thuế suất hiện tại
- **Console log**: Thông báo lỗi từ Developer Tools

#### Cách Báo Cáo Lỗi

1. Mô tả chi tiết các bước tái tạo lỗi
2. Screenshot màn hình lỗi
3. Copy/paste thông báo lỗi từ console
4. Đính kèm file mẫu (nếu có thể)

---

## Câu Hỏi Thường Gặp

### Về Tính Năng

**Q: Ứng dụng có hỗ trợ file Excel không?**
A: Hiện tại chỉ hỗ trợ file XML và JSON. Bạn cần chuyển đổi Excel sang XML trước khi sử dụng.

**Q: Có thể xử lý bao nhiêu file cùng lúc?**
A: Không có giới hạn cứng, nhưng khuyến nghị không quá 100 file để đảm bảo hiệu suất tốt.

**Q: Dữ liệu có được gửi lên server không?**
A: Không. Tất cả xử lý diễn ra trên trình duyệt của bạn, đảm bảo bảo mật tuyệt đối.

**Q: Có thể sử dụng offline không?**
A: Có, sau khi tải trang lần đầu, bạn có thể sử dụng offline (trừ khi cần cập nhật).

### Về Cấu Hình

**Q: Làm sao để tạo quy tắc thuế suất phức tạp?**
A: Sử dụng nhiều điều kiện trong một quy tắc (logic AND) hoặc tạo nhiều quy tắc riêng biệt (logic OR).

**Q: Pattern matching có phân biệt hoa thường không?**
A: Không, tất cả so sánh đều không phân biệt hoa thường.

**Q: Có thể tạo điều kiện dựa trên số tiền không?**
A: Hiện tại chưa hỗ trợ. Chỉ hỗ trợ điều kiện dựa trên text.

### Về Xác Thực

**Q: Tại sao địa chỉ không khớp dù nhìn giống nhau?**
A: Có thể do khác biệt về viết tắt, dấu câu, hoặc khoảng trắng. Ứng dụng đã hỗ trợ nhiều định dạng viết tắt phổ biến.

**Q: Làm sao thêm công ty mới vào danh sách kiểm tra?**
A: Hiện tại danh sách công ty được hard-code. Trong phiên bản tương lai sẽ hỗ trợ import danh sách tùy chỉnh.

**Q: Kết quả xác thực có được lưu lại không?**
A: Không, kết quả chỉ hiển thị trong phiên làm việc hiện tại. Bạn cần screenshot hoặc copy kết quả nếu muốn lưu.

### Về Kỹ Thuật

**Q: Tại sao ứng dụng chạy chậm?**
A: Có thể do file quá lớn, quá nhiều quy tắc phức tạp, hoặc trình duyệt thiếu RAM. Thử đóng các tab khác và chia nhỏ file.

**Q: Có hỗ trợ trình duyệt nào khác ngoài Chrome không?**
A: Có, hỗ trợ Firefox, Edge, Safari. Tuy nhiên Chrome có hiệu suất tốt nhất.

**Q: Làm sao backup toàn bộ cấu hình?**
A: Sử dụng tính năng "Xuất cấu hình" để tạo file backup JSON.

### Về Bảo Mật

**Q: Dữ liệu hóa đơn có được lưu trữ ở đâu không?**
A: Không, tất cả dữ liệu chỉ tồn tại trong RAM và bị xóa khi đóng trình duyệt.

**Q: Có thể sử dụng trong môi trường doanh nghiệp không?**
A: Có, ứng dụng hoàn toàn client-side, không cần kết nối internet sau khi tải.

**Q: Cấu hình thuế suất có được mã hóa không?**
A: Cấu hình được lưu dưới dạng JSON trong LocalStorage, không mã hóa. Nếu cần bảo mật cao, hãy xuất và lưu file ở nơi an toàn.

---

## Kết Luận

Ứng dụng **Xác Thực Hóa Đơn Thuế Việt Nam** là công cụ mạnh mẽ và linh hoạt để kiểm tra tính chính xác của hóa đơn điện tử. Với giao diện thân thiện và tính năng phong phú, ứng dụng giúp bạn:

### Lợi Ích Chính

- ✅ **Tiết kiệm thời gian**: Xử lý hàng loạt file XML
- ✅ **Đảm bảo chính xác**: Kiểm tra tự động theo quy tắc
- ✅ **Bảo mật cao**: Xử lý hoàn toàn offline
- ✅ **Linh hoạt**: Cấu hình quy tắc tùy chỉnh
- ✅ **Dễ sử dụng**: Giao diện trực quan, hỗ trợ tiếng Việt

### Khuyến Nghị Sử Dụng

1. **Backup thường xuyên**: Xuất cấu hình định kỳ
2. **Test trước khi áp dụng**: Thử nghiệm với file mẫu
3. **Cập nhật quy tắc**: Điều chỉnh theo thay đổi quy định
4. **Kiểm tra kết quả**: Xem xét kỹ các cảnh báo và lỗi

### Phát Triển Tương Lai

- 🔄 **Import danh sách công ty**: Tùy chỉnh database tham chiếu
- 📊 **Báo cáo chi tiết**: Xuất báo cáo Excel/PDF
- 🔍 **Tìm kiếm nâng cao**: Lọc và tìm kiếm trong kết quả
- 🌐 **API integration**: Kết nối với hệ thống ERP
- 📱 **Mobile app**: Ứng dụng di động

---

_Hướng dẫn này được cập nhật lần cuối: Tháng 1/2025_
_Phiên bản ứng dụng: 1.0.0_

Nếu bạn có thắc mắc hoặc góp ý, vui lòng liên hệ qua các kênh hỗ trợ chính thức.
