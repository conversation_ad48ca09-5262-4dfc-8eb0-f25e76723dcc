# Folder Explorer

- prod: https://hd.ttmi.pro/

A modern React application built with Vite, TypeScript, and Shadcn/ui that allows users to drag and drop folders to explore their contents in a beautiful table interface.


## Features

*(Note: The application user interface is in Vietnamese.)*

- 🗂️ **Drag & Drop Folder Support** - Drop any folder to instantly view its contents.
- 📂 **Choose Folder/Files Button** - Alternatively, select folders or multiple files using the system file dialog.
- 📋 **File List Table** - Clean, sortable table showing file names, paths, types, and sizes.
- 🎨 **Modern UI** - Built with Shadcn/ui and Tailwind CSS for a polished experience
- 📱 **Responsive Design** - Works seamlessly on desktop and mobile devices
- 🔍 **File Type Icons** - Visual indicators for files and directories
- ⚡ **Fast & Lightweight** - Powered by Vite for lightning-fast development and builds
- 🌗 **Dark Mode Support** - Automatic dark/light theme switching
- 📏 **Resizable Panels** - Adjustable sidebar and content areas

## Technology Stack

- **Frontend Framework**: React 19 with TypeScript
- **Build Tool**: Vite 6
- **UI Components**: Shadcn/ui
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **File System**: Modern File System Access API with fallback support

## Getting Started

### Prerequisites

- Node.js 18+ 
- pnpm (recommended) or npm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd ttmi_validate
```

2. Install dependencies:
```bash
pnpm install
```

3. Start the development server:
```bash
pnpm dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Building for Production

```bash
pnpm build
```

The built files will be available in the `dist` directory.

### Linting

```bash
pnpm lint
```

## Usage

*(Note: The application user interface is in Vietnamese.)*

### Main Features

The application provides two main functionalities accessible through direct URL navigation:

#### 1. Folder/File Explorer (Home - `/`)
1. **Open the Application** - Navigate to the application in your browser.
2. **Load Files/Folders**:
    - **Drag & Drop**: Drag any folder from your file system into the designated drop zone.
    - **Choose Button**: Click the "Chọn thư mục / tệp" (Choose Folder / Files) button to open your system's file dialog. You can select a folder (if your browser supports `webkitdirectory`) or multiple files.
3. **Explore Files** - View the complete file listing in the table below.
4. **Clear Results** - Use the "Xóa tệp" (Clear Files) button to reset and try another folder/selection.

#### 2. Excel File Analyzer (`/pv`)
1. **Navigate to Excel Analyzer** - Go directly to `/pv` in your browser.
2. **Upload Excel File**:
    - **Drag & Drop**: Drag an Excel file (.xlsx, .xls, .xlsm) into the upload area.
    - **File Picker**: Click "Chọn file Excel" to browse and select a file.
3. **File Processing**: The application will automatically:
    - Use **Row 8** as column headers
    - **Skip Row 9** completely
    - Parse data starting from **Row 10** onwards
4. **View Results**: Browse the parsed data in an interactive table with:
    - Search functionality across all columns
    - Pagination for large datasets
    - Export capability to download filtered results
5. **Customer Selection**: If column I contains customer names, you can:
    - View all unique customers in a filterable table
    - Select/deselect customers using checkboxes
    - Use "Check All" to select all filtered customers
    - Search for specific customers by name
    - View selected customers in a separate list below
6. **Upload New File**: Click "Tải file mới" to analyze another Excel file.

### Excel File Requirements
- **Supported formats**: .xlsx, .xls, .xlsm
- **Maximum file size**: 10MB
- **Required structure**:
  - Row 8: Column headers (required)
  - Row 9: Will be skipped
  - Row 10+: Data rows
- **Customer Selection Feature**:
  - Column I: Customer names (for customer selection functionality)
  - If column I contains customer names, the app will automatically extract unique customers
  - Provides filterable customer selection with checkboxes

## Browser Compatibility

This application uses the modern File System Access API for enhanced folder reading capabilities via drag & drop. The "Choose Folder / Files" button relies on standard file input capabilities, with `webkitdirectory` for folder selection where supported.

- **Drag & Drop (Full Folder Support)**: Chrome 86+, Edge 86+, Opera 72+
- **Drag & Drop (File-only)**: Firefox, Safari
- **Choose Folder/Files Button**:
    - Folder Selection (`webkitdirectory`): Chrome, Edge, Opera, and other Chromium-based browsers.
    - Multiple File Selection: Most modern browsers.
    - Single File Selection: All browsers.

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Shadcn/ui components
│   ├── ExcelUpload.tsx # Excel file upload component
│   ├── ExcelDataTable.tsx # Excel data display table
│   └── CustomerSelector.tsx # Customer selection with checkboxes
├── layouts/            # Page layouts
│   └── MainLayout.tsx  # Main application layout
├── pages/              # Application pages
│   ├── FolderDropPage.tsx # Main drag & drop functionality
│   └── PVPage.tsx      # Excel file analyzer page
├── hooks/              # Custom React hooks
│   └── useExcelParser.ts # Excel parsing logic
├── utils/              # Utility functions
│   └── excelUtils.ts   # Excel processing utilities
├── types/              # TypeScript type definitions
│   └── excel.ts        # Excel-related types
├── lib/                # Utility functions
└── App.tsx             # Application entry point with routing
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'feat: add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Shadcn/ui](https://ui.shadcn.com/) for the beautiful component library
- [Lucide](https://lucide.dev/) for the icon set
- [Tailwind CSS](https://tailwindcss.com/) for the utility-first styling
- [Vite](https://vitejs.dev/) for the fast build tool
