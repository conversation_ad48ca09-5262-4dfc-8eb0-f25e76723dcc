/**
 * String utility functions for text processing and normalization
 */

/**
 * Removes Vietnamese diacritics from a string
 * Converts accented characters to their base form
 * 
 * @param str - The string to remove diacritics from
 * @returns String without diacritics
 * 
 * @example
 * removeDiacritics("Thùng") // "Thung"
 * removeDiacritics("Cái") // "Cai"
 * removeDiacritics("Hộp") // "Hop"
 */
export function removeDiacritics(str: string): string {
  return str
    .normalize('NFD')                    // Decompose characters into base + combining marks
    .replace(/[\u0300-\u036f]/g, '')    // Remove combining diacritical marks
    .replace(/đ/g, 'd')                 // Handle lowercase đ specifically
    .replace(/Đ/g, 'D');                // Handle uppercase Đ specifically
}

/**
 * Normalizes a string by trimming whitespace and converting to lowercase
 * 
 * @param str - The string to normalize
 * @returns Normalized string
 */
export function normalizeString(str: string): string {
  return str.trim().toLowerCase();
}

/**
 * Normalizes a string and removes diacritics
 * Combines normalization and diacritics removal for consistent comparison
 * 
 * @param str - The string to normalize and clean
 * @returns Normalized string without diacritics
 * 
 * @example
 * normalizeAndRemoveDiacritics("  Thùng  ") // "thung"
 * normalizeAndRemoveDiacritics("CÁI") // "cai"
 */
export function normalizeAndRemoveDiacritics(str: string): string {
  return removeDiacritics(normalizeString(str));
}

/**
 * Checks if two strings are equal when normalized and without diacritics
 * Useful for comparing Vietnamese text that may have different diacritics
 * 
 * @param str1 - First string to compare
 * @param str2 - Second string to compare
 * @returns True if strings are equal when normalized
 * 
 * @example
 * isEqualIgnoreDiacritics("Thùng", "THUNG") // true
 * isEqualIgnoreDiacritics("Cái", "cai") // true
 * isEqualIgnoreDiacritics("Hộp", "Bao") // false
 */
export function isEqualIgnoreDiacritics(str1: string, str2: string): boolean {
  return normalizeAndRemoveDiacritics(str1) === normalizeAndRemoveDiacritics(str2);
}

/**
 * Capitalizes the first letter of a string
 * 
 * @param str - The string to capitalize
 * @returns String with first letter capitalized
 */
export function capitalize(str: string): string {
  if (!str) return str;
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Converts a string to title case (capitalizes each word)
 * 
 * @param str - The string to convert to title case
 * @returns String in title case
 */
export function toTitleCase(str: string): string {
  return str
    .toLowerCase()
    .split(' ')
    .map(word => capitalize(word))
    .join(' ');
}

/**
 * Removes extra whitespace and normalizes spacing
 * 
 * @param str - The string to clean
 * @returns String with normalized spacing
 */
export function cleanWhitespace(str: string): string {
  return str.trim().replace(/\s+/g, ' ');
}

/**
 * Checks if a string is empty or contains only whitespace
 * 
 * @param str - The string to check
 * @returns True if string is empty or whitespace only
 */
export function isEmpty(str: string | null | undefined): boolean {
  return !str || str.trim().length === 0;
}

/**
 * Truncates a string to a specified length and adds ellipsis if needed
 * 
 * @param str - The string to truncate
 * @param maxLength - Maximum length of the string
 * @param ellipsis - String to append when truncated (default: "...")
 * @returns Truncated string
 */
export function truncate(str: string, maxLength: number, ellipsis: string = "..."): string {
  if (str.length <= maxLength) return str;
  return str.slice(0, maxLength - ellipsis.length) + ellipsis;
}

/**
 * Escapes special characters for use in regular expressions
 * 
 * @param str - The string to escape
 * @returns Escaped string safe for regex
 */
export function escapeRegex(str: string): string {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Converts a string to a URL-friendly slug
 *
 * @param str - The string to convert to slug
 * @returns URL-friendly slug
 */
export function toSlug(str: string): string {
  return normalizeAndRemoveDiacritics(str)
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-')         // Replace spaces with hyphens
    .replace(/-+/g, '-')          // Replace multiple hyphens with single
    .replace(/^-|-$/g, '');       // Remove leading/trailing hyphens
}

/**
 * Standardizes unit names to uppercase format using regex patterns
 * Removes diacritics and converts to uppercase for consistency
 *
 * @param unit - The unit name to standardize
 * @returns Standardized unit name in uppercase without diacritics
 *
 * @example
 * standardizeUnitName("Thùng") // "THUNG"
 * standardizeUnitName("cái") // "CAI"
 * standardizeUnitName("  hộp  ") // "HOP"
 */
export function standardizeUnitName(unit: string): string {
  if (!unit || typeof unit !== 'string') {
    return '';
  }

  // Normalize: trim, lowercase, remove diacritics
  const normalized = normalizeAndRemoveDiacritics(unit);

  // Convert to uppercase for consistency
  return normalized.toUpperCase();
}
