import type {
  FileEntry,
  FileSystemDirectoryHandle,
  FileSystemFileHandle,
} from "@/types/fileSystem";
import type { ValidationRule, TaxRateConfig } from "@/types/validation";
import { xmlToJson } from "./xmlUtils"; // Changed from xmlToRows to xmlToJson
import { validateInvoice } from "./validationUtils";
import { SAMPLE_VALIDATION_RULES } from "@/data/sampleValidationRules";

// Helper function to read file content as text
const readFileAsText = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
    reader.readAsText(file);
  });
};

/**
 * Recursively retrieves file and directory entries from a given directory handle.
 * Uses the File System Access API.
 * @param directoryHandle - The handle to the directory to read.
 * @param currentPath - The current relative path, used for constructing full paths.
 * @param validationRules - Optional validation rules to use for XML files
 * @returns A promise that resolves to an array of FileEntry objects.
 */
export const getFilePaths = async (
  directoryHandle: FileSystemDirectoryHandle,
  currentPath = "",
  validationRules: ValidationRule[] = SAMPLE_VALIDATION_RULES,
  taxRateConfig?: TaxRateConfig
): Promise<FileEntry[]> => {
  const filePaths: FileEntry[] = [];

  try {
    for await (const [name, handle] of directoryHandle.entries()) {
      const relativePath = currentPath ? `${currentPath}/${name}` : name;

      if (handle.kind === "file") {
        const fileHandle = handle as FileSystemFileHandle;
        const file = await fileHandle.getFile();
        const fileEntry: FileEntry = {
          name,
          path: relativePath,
          type: "file",
          size: file.size,
        };

        if (file.name.toLowerCase().endsWith(".xml")) {
          try {
            fileEntry.rawContent = await readFileAsText(file);
            if (fileEntry.rawContent) {
              fileEntry.jsonData = xmlToJson(fileEntry.rawContent); // Use xmlToJson and store in jsonData

              // Perform validation if JSON parsing was successful
              if (fileEntry.jsonData && !("error" in fileEntry.jsonData)) {
                fileEntry.validationResult = validateInvoice(
                  file.name,
                  fileEntry.jsonData as Record<string, unknown>,
                  validationRules,
                  taxRateConfig
                );
              }
            }
          } catch (e) {
            console.error(`Error reading or parsing XML file ${file.name}:`, e);
            fileEntry.jsonData = {
              error: `Failed to process XML: ${
                e instanceof Error ? e.message : String(e)
              }`,
            };
          }
        }
        filePaths.push(fileEntry);
      } else if (handle.kind === "directory") {
        filePaths.push({
          name,
          path: relativePath,
          type: "directory",
        });
        // Recursively get files from subdirectories
        const dirHandle = handle as FileSystemDirectoryHandle;
        const subFiles = await getFilePaths(
          dirHandle,
          relativePath,
          validationRules,
          taxRateConfig
        );
        filePaths.push(...subFiles);
      }
    }
  } catch (error) {
    console.error("Error reading directory:", error);
    // Optionally, re-throw or handle more gracefully
  }

  return filePaths.sort((a, b) => a.path.localeCompare(b.path));
};

/**
 * Processes a FileList object (typically from an <input type="file"> element)
 * and converts it into an array of FileEntry objects.
 * This is a fallback/alternative to the File System Access API.
 * It relies on `webkitRelativePath` for folder structure, which is non-standard.
 * @param fileList - The FileList to process.
 * @param validationRules - Optional validation rules to use for XML files
 * @returns A promise that resolves to an array of FileEntry objects.
 */
export const processFileList = async (
  fileList: FileList,
  validationRules: ValidationRule[] = SAMPLE_VALIDATION_RULES,
  taxRateConfig?: TaxRateConfig
): Promise<FileEntry[]> => {
  const processedFiles: FileEntry[] = [];
  for (let i = 0; i < fileList.length; i++) {
    const file = fileList[i];
    const path =
      (file as File & { webkitRelativePath?: string }).webkitRelativePath ||
      file.name;

    // Heuristic to determine if an entry is a directory based on webkitRelativePath.
    // This is imperfect, especially for empty directories.
    let isDirectory = false;
    const currentWebkitPath = (file as File & { webkitRelativePath?: string })
      .webkitRelativePath;

    if (currentWebkitPath) {
      // If webkitRelativePath is present and different from the file name, it's part of a path.
      if (currentWebkitPath !== file.name) {
        // This could be a file in a subdirectory or a directory entry itself.
        // A common pattern for directory entries from <input webkitdirectory> is that
        // they might have size 0 and an empty type string.
        if (file.size === 0 && file.type === "") {
          isDirectory = true;
        }
      }
      // More robust check: if the next file's path starts with the current file's path + '/',
      // then the current file is a directory.
      if (i + 1 < fileList.length) {
        const nextFile = fileList[i + 1];
        const nextWebkitPath = (
          nextFile as File & { webkitRelativePath?: string }
        ).webkitRelativePath;
        if (
          nextWebkitPath &&
          nextWebkitPath.startsWith(currentWebkitPath + "/")
        ) {
          isDirectory = true;
        }
      }
    }
    const fileEntry: FileEntry = {
      name: file.name,
      path: path,
      type: isDirectory ? "directory" : "file",
      size: file.size,
    };

    if (!isDirectory && file.name.toLowerCase().endsWith(".xml")) {
      try {
        fileEntry.rawContent = await readFileAsText(file);
        if (fileEntry.rawContent) {
          fileEntry.jsonData = xmlToJson(fileEntry.rawContent); // Use xmlToJson and store in jsonData

          // Perform validation if JSON parsing was successful
          if (fileEntry.jsonData && !("error" in fileEntry.jsonData)) {
            fileEntry.validationResult = validateInvoice(
              file.name,
              fileEntry.jsonData as Record<string, unknown>,
              validationRules,
              taxRateConfig
            );
          }
        }
      } catch (e) {
        console.error(`Error reading or parsing XML file ${file.name}:`, e);
        fileEntry.jsonData = {
          error: `Failed to process XML: ${
            e instanceof Error ? e.message : String(e)
          }`,
        };
      }
    }

    processedFiles.push(fileEntry);
  }
  // Sort files by path for consistent order
  return processedFiles.sort((a, b) => a.path.localeCompare(b.path));
};

/**
 * Formats a file size in bytes into a human-readable string (KB, MB, GB).
 * @param bytes - The file size in bytes.
 * @returns A human-readable file size string, or '-' if bytes is undefined or zero.
 */
export const formatFileSize = (bytes?: number): string => {
  if (!bytes) return "-";
  if (bytes === 0) return "0 Bytes"; // Handle 0 byte files explicitly
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"]; // Added TB for larger files
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  // Ensure 'i' is within the bounds of the 'sizes' array
  const unitIndex = Math.min(i, sizes.length - 1);

  return (
    parseFloat((bytes / Math.pow(k, unitIndex)).toFixed(2)) +
    " " +
    sizes[unitIndex]
  );
};

/**
 * Re-validates existing files with new validation rules
 * @param files - Array of FileEntry objects to re-validate
 * @param validationRules - New validation rules to apply
 * @param taxRateConfig - Optional tax rate configuration for validation
 * @returns Promise that resolves to updated FileEntry array
 */
export const revalidateFiles = async (
  files: FileEntry[],
  validationRules: ValidationRule[],
  taxRateConfig?: TaxRateConfig
): Promise<FileEntry[]> => {
  const revalidatedFiles: FileEntry[] = [];

  for (const file of files) {
    const updatedFile = { ...file };

    // Only re-validate XML files that have jsonData
    if (
      file.name.toLowerCase().endsWith(".xml") &&
      file.jsonData &&
      !("error" in file.jsonData)
    ) {
      try {
        updatedFile.validationResult = validateInvoice(
          file.name,
          file.jsonData as Record<string, unknown>,
          validationRules,
          taxRateConfig
        );
      } catch (error) {
        console.error(`Error re-validating file ${file.name}:`, error);
        // Keep the original validation result if re-validation fails
      }
    }

    revalidatedFiles.push(updatedFile);
  }

  return revalidatedFiles;
};
