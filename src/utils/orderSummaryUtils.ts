import type { ExcelParseResult } from "@/types/excel";
import type { UnitConversionData } from "@/types/unitConversion";
import { standardizeUnitName } from "@/utils/stringUtils";



export interface OrderSummaryItem {
  "Tên Vật Tư": string;
  DvT: string;
  Tổng: number;
  [key: string]: string | number; // Dynamic columns including "Mã Vật Tư", "Hệ số", and customer columns
}

export interface OrderSummaryResult {
  items: OrderSummaryItem[];
  customers: string[];
  totalItems: number;
  uploadDate?: Date; // Date when the original data file was uploaded
}

/**
 * Creates order summary from Excel data for selected customers
 */
export function createOrderSummary(
  excelData: ExcelParseResult,
  selectedCustomers: string[],
  exportDate?: string,
  conversionData?: UnitConversionData[],
  uploadDate?: Date
): OrderSummaryResult {
  // Find required columns
  const customerColumnHeader = excelData.headers.find(
    (header) =>
      header.column === "I" ||
      header.index === 8 ||
      header.name.toLowerCase().includes("tên khách hàng")
  );

  const materialCodeColumnHeader = excelData.headers.find(
    (header) =>
      header.column === "K" || header.name.toLowerCase().includes("mã vật tư")
  );

  const materialColumnHeader = excelData.headers.find(
    (header) =>
      header.column === "L" || header.name.toLowerCase().includes("tên vật tư")
  );

  const unitColumnHeader = excelData.headers.find(
    (header) =>
      header.column === "M" || header.name.toLowerCase().includes("đvt")
  );

  const quantityColumnHeader = excelData.headers.find(header =>
    header.column === 'N' ||
    header.name.toLowerCase().includes('số lượng')

  );

  // Find export date column (Column D - "Ngày xuất")
  const exportDateColumnHeader = excelData.headers.find(header =>
    header.column === 'D' ||
    header.name.toLowerCase().includes('ngày xuất')
  );

  if (
    !customerColumnHeader ||
    !materialColumnHeader ||
    !unitColumnHeader ||
    !quantityColumnHeader
  ) {
    return { items: [], customers: selectedCustomers, totalItems: 0 };
  }

  // Filter rows for selected customers and export date (if specified)
  const relevantRows = excelData.rows.filter(row => {
    const customerName = row.values[customerColumnHeader.name];
    const customerMatches = customerName && selectedCustomers.includes(String(customerName).trim());

    // If no export date is specified, include all customer matches
    if (!exportDate || !exportDateColumnHeader) {
      return customerMatches;
    }

    // If export date is specified, also filter by export date
    const rowExportDate = row.values[exportDateColumnHeader.name];
    const exportDateMatches = rowExportDate && String(rowExportDate).trim() === exportDate.trim();

    return customerMatches && exportDateMatches;
  });



  // Create conversion factor lookup map
  const conversionFactorMap = new Map<string, number>();
  if (conversionData) {
    conversionData.forEach(item => {
      conversionFactorMap.set(item.materialCode, item.conversionFactor);
    });
  }

  // Group by material and unit
  const materialGroups = new Map<
    string,
    {
      materialCode?: string;
      unit: string;
      customerQuantities: Map<string, number>;
      total: number;
    }
  >();

  relevantRows.forEach((row) => {
    const customerName = String(
      row.values[customerColumnHeader.name] || ""
    ).trim();
    const materialCode = materialCodeColumnHeader
      ? String(row.values[materialCodeColumnHeader.name] || "").trim()
      : "";
    const materialName = String(
      row.values[materialColumnHeader.name] || ""
    ).trim();
    const rawUnit = String(row.values[unitColumnHeader.name] || "").trim();
    const unit = standardizeUnitName(rawUnit);
    const quantityStr = String(row.values[quantityColumnHeader.name] || "0");

    // Parse quantity - handle various number formats
    let quantity = 0;
    const cleanQuantityStr = quantityStr
      .replace(/[^\d.,]/g, "")
      .replace(",", ".");
    const parsedQuantity = parseFloat(cleanQuantityStr);
    if (!isNaN(parsedQuantity)) {
      quantity = parsedQuantity;
    }

    if (!materialName || !customerName) return;

    const key = `${materialName}|${unit}`;

    if (!materialGroups.has(key)) {
      materialGroups.set(key, {
        materialCode,
        unit,
        customerQuantities: new Map(),
        total: 0,
      });
    }

    const group = materialGroups.get(key)!;
    const currentQuantity = group.customerQuantities.get(customerName) || 0;
    group.customerQuantities.set(customerName, currentQuantity + quantity);
    group.total += quantity;
  });

  // Convert to result format with enhanced unit conversion logic
  const items: OrderSummaryItem[] = [];

  materialGroups.forEach((group, key) => {
    const [materialName, unit] = key.split("|");

    // Find conversion data for this material
    const conversionItem = conversionData?.find(conv =>
      conv.materialCode.toLowerCase().trim() === group.materialCode?.toLowerCase().trim()
    );

    if (!conversionItem) {
      // No conversion data found - keep original values with conversion factor = 1
      const item: OrderSummaryItem = {
        "Tên Vật Tư": materialName,
        DvT: unit,
        Tổng: group.total,
        "Hệ số": 1,
      };

      if (group.materialCode) {
        item["Mã Vật Tư"] = group.materialCode;
      }

      selectedCustomers.forEach((customer) => {
        item[customer] = group.customerQuantities.get(customer) || 0;
      });

      items.push(item);
      return;
    }

    // Conversion data found - standardize and compare unit names
    const originalUnit = unit; // Already standardized
    const conversionUnit = standardizeUnitName(conversionItem.unit);
    const conversionFactor = conversionItem.conversionFactor;

    if (originalUnit === conversionUnit) {
      // Same units - keep original values with conversion factor
      const item: OrderSummaryItem = {
        "Tên Vật Tư": materialName,
        DvT: unit,
        Tổng: group.total,
        "Hệ số": conversionFactor,
      };

      if (group.materialCode) {
        item["Mã Vật Tư"] = group.materialCode;
      }

      selectedCustomers.forEach((customer) => {
        item[customer] = group.customerQuantities.get(customer) || 0;
      });

      items.push(item);
      return;
    }

    // Different units - apply quantity conversion logic
    if (group.total < conversionFactor) {
      // Total < Conversion Factor - keep single row with original unit and conversion factor = 1
      const item: OrderSummaryItem = {
        "Tên Vật Tư": materialName,
        DvT: unit, // Keep original unit
        Tổng: group.total,
        "Hệ số": 1, // Use conversion factor = 1 for original unit
      };

      if (group.materialCode) {
        item["Mã Vật Tư"] = group.materialCode;
      }

      selectedCustomers.forEach((customer) => {
        item[customer] = group.customerQuantities.get(customer) || 0;
      });

      items.push(item);
    } else {
      // Total >= Conversion Factor - split into TWO separate rows (must be consecutive)
      const convertedQuantity = Math.floor(group.total / conversionFactor);
      const remainderQuantity = group.total % conversionFactor;

      // Row 1: Converted Unit (e.g., "THUNG")
      if (convertedQuantity > 0) {
        const convertedItem: OrderSummaryItem = {
          "Tên Vật Tư": materialName,
          DvT: conversionUnit, // Use standardized unit from conversion data
          Tổng: convertedQuantity,
          "Hệ số": conversionFactor,
        };

        if (group.materialCode) {
          convertedItem["Mã Vật Tư"] = group.materialCode;
        }

        // Calculate converted quantities for each customer
        selectedCustomers.forEach((customer) => {
          const customerTotal = group.customerQuantities.get(customer) || 0;
          const customerConverted = Math.floor(customerTotal / conversionFactor);
          convertedItem[customer] = customerConverted;
        });

        items.push(convertedItem);
      }

      // Row 2: Remainder Unit (e.g., "Bao") - must be consecutive with Row 1
      if (remainderQuantity > 0) {
        const remainderItem: OrderSummaryItem = {
          "Tên Vật Tư": materialName,
          DvT: unit, // Use original unit
          Tổng: remainderQuantity,
          "Hệ số": 1, // Remainder uses conversion factor = 1
        };

        if (group.materialCode) {
          remainderItem["Mã Vật Tư"] = group.materialCode;
        }

        // Calculate remainder quantities for each customer
        selectedCustomers.forEach((customer) => {
          const customerTotal = group.customerQuantities.get(customer) || 0;
          const customerRemainder = customerTotal % conversionFactor;
          remainderItem[customer] = customerRemainder;
        });

        items.push(remainderItem);
      }
    }
  });

  items.sort((a, b) => {
    const unitA = String(a["DvT"] || "").trim();
    const unitB = String(b["DvT"] || "").trim();
    const materialA = String(a["Tên Vật Tư"] || "").trim();
    const materialB = String(b["Tên Vật Tư"] || "").trim();

    const unitComparison = unitA.localeCompare(unitB, "vi", {
      sensitivity: "base",
      numeric: true,
      ignorePunctuation: true,
    });

    if (unitComparison !== 0) {
      return unitComparison;
    }

    return materialA.localeCompare(materialB, "vi", {
      sensitivity: "base",
      numeric: true,
      ignorePunctuation: true,
    });
  });

  return {
    items,
    customers: selectedCustomers,
    totalItems: items.length,
    uploadDate,
  };
}

/**
 * A5 paper formatting configuration for Excel exports
 */
interface A5FormatConfig {
  // A5 dimensions: 148mm x 210mm (5.83" x 8.27")
  pageMargins: {
    left: number;
    right: number;
    top: number;
    bottom: number;
    header: number;
    footer: number;
  };
  fontSize: number;
  rowHeight: number;
  maxColumnWidth: number;
  orientation: "portrait" | "landscape";
}

const A5_FORMAT_CONFIG: A5FormatConfig = {
  pageMargins: {
    left: 0.5, // 0.5 inch margins for A5
    right: 0.5,
    top: 0.75,
    bottom: 0.75,
    header: 0.3,
    footer: 0.3,
  },
  fontSize: 9, // Smaller font for A5 readability
  rowHeight: 15, // Compact row height
  maxColumnWidth: 20, // Maximum column width for A5
  orientation: "portrait",
};

/**
 * Applies A5 paper formatting to an Excel worksheet
 */
function applyA5Formatting(worksheet: any, XLSX: any): void {
  worksheet["!margins"] = A5_FORMAT_CONFIG.pageMargins;

  if (!worksheet["!pageSetup"]) {
    worksheet["!pageSetup"] = {};
  }

  worksheet["!pageSetup"] = {
    ...worksheet["!pageSetup"],
    paperSize: 11, // A5 paper size code
    orientation: A5_FORMAT_CONFIG.orientation,
    scale: 100, // 100% scaling
    fitToWidth: 1, // Fit to 1 page wide
    fitToHeight: 0, // Allow multiple pages vertically
    horizontalDpi: 300,
    verticalDpi: 300,
  };

  // Set print area and page breaks
  if (!worksheet["!printHeader"]) {
    worksheet["!printHeader"] = {};
  }

  const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");
  for (let row = range.s.r; row <= range.e.r; row++) {
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
      if (worksheet[cellAddress]) {
        if (!worksheet[cellAddress].s) {
          worksheet[cellAddress].s = {};
        }
        worksheet[cellAddress].s.font = {
          name: "Arial",
          sz: A5_FORMAT_CONFIG.fontSize,
        };

        if (!worksheet["!rows"]) {
          worksheet["!rows"] = [];
        }
        if (!worksheet["!rows"][row]) {
          worksheet["!rows"][row] = {};
        }
        worksheet["!rows"][row].hpt = A5_FORMAT_CONFIG.rowHeight;
      }
    }
  }
}

/**
 * Calculates optimal column widths for A5 paper with enhanced columns
 */
function calculateA5ColumnWidths(
  baseColumns: string[],
  customerCount: number
): Array<{ wch: number }> {
  const availableWidth = 45; // Approximate usable width for A5 in character units

  // Allocate width based on content importance for enhanced columns
  const widths = [
    { wch: 10 }, // Mã Vật Tư - material code
    { wch: Math.min(16, A5_FORMAT_CONFIG.maxColumnWidth) }, // Tên Vật Tư - product names (reduced to fit more columns)
    { wch: 6 },  // DvT - unit (compact)
    { wch: 6 },  // Tổng - total quantity (compact)
    { wch: 6 },  // Hệ Số - conversion factor (compact)
  ];

  // Calculate remaining width for customer columns
  const usedWidth = widths.reduce((sum, w) => sum + w.wch, 0);
  const remainingWidth = Math.max(
    availableWidth - usedWidth,
    customerCount * 5 // Reduced customer column width to fit more columns
  );
  const customerColumnWidth = Math.min(
    Math.floor(remainingWidth / customerCount),
    A5_FORMAT_CONFIG.maxColumnWidth
  );

  // Add customer columns
  for (let i = 0; i < customerCount; i++) {
    widths.push({ wch: customerColumnWidth });
  }

  return widths;
}

/**
 * Exports order summary to Excel format with enhanced unit standardization
 * Includes material code, standardized unit names, conversion factors, and customer quantities
 */
export function exportOrderSummaryToExcel(
  summary: OrderSummaryResult,
  filename?: string
): void {
  // Dynamic import to avoid bundling xlsx in main chunk
  import('xlsx').then((XLSX) => {
    // Format the upload date in Vietnamese format (DD/MM/YYYY HH:mm)
    let formattedUploadDate = '';
    if (summary.uploadDate) {
      const uploadDate = new Date(summary.uploadDate);
      formattedUploadDate = uploadDate.toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });
    }

    // Prepare data for Excel export with enhanced columns
    const exportData = summary.items.map(item => {
      const row: Record<string, any> = {
        'Mã Vật Tư': item['Mã Vật Tư'] || '', // Material code
        'Tên Vật Tư': item['Tên Vật Tư'], // Material name
        'DvT': item['DvT'], // Standardized unit name
        'Tổng': item['Tổng'], // Total quantity
        'Hệ Số': item['Hệ số'] || 1, // Conversion factor
      };

      // Add customer columns
      summary.customers.forEach(customer => {
        row[customer] = item[customer] || 0;
      });

      // Add upload date as the last column
      row['Ngày đăng tải'] = formattedUploadDate;

      return row;
    });

    // Create worksheet
    const worksheet = XLSX.utils.json_to_sheet(exportData);

    // Apply enhanced A5-optimized column widths for new columns including upload date
    const baseColumns = ['Mã Vật Tư', 'Tên Vật Tư', 'DvT', 'Tổng', 'Hệ Số'];
    const columnWidths = calculateA5ColumnWidths(
      baseColumns,
      summary.customers.length + 1 // +1 for the upload date column
    );
    worksheet['!cols'] = columnWidths;

    // Apply A5 formatting
    applyA5Formatting(worksheet, XLSX);

    // Add header styling for better readability
    const headerRange = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    for (let col = headerRange.s.c; col <= headerRange.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
      if (!worksheet[cellAddress]) continue;

      worksheet[cellAddress].s = {
        font: { bold: true, color: { rgb: "FFFFFF" } },
        fill: { fgColor: { rgb: "366092" } },
        alignment: { horizontal: "center", vertical: "center" },
        border: {
          top: { style: "thin", color: { rgb: "000000" } },
          bottom: { style: "thin", color: { rgb: "000000" } },
          left: { style: "thin", color: { rgb: "000000" } },
          right: { style: "thin", color: { rgb: "000000" } }
        }
      };
    }

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Tổng Hợp Đơn Hàng');

    // Generate filename with current date and time for uniqueness
    let exportFilename = filename;
    if (!exportFilename) {
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0];
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '');
      exportFilename = `tong-hop-don-hang-${dateStr}-${timeStr}.xlsx`;
    }

    // Save file
    XLSX.writeFile(workbook, exportFilename);
  }).catch(error => {
    console.error('Error exporting to Excel:', error);
    alert('Lỗi khi xuất file Excel. Vui lòng thử lại.');
  });
}
