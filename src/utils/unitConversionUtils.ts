import type {
  UnitConversionData,
  UnitConversionResult,
  UnitConversionError
} from '@/types/unitConversion';
import { parseExcelFile, validateExcelFile } from '@/utils/excelUtils';
import { standardizeUnitName } from '@/utils/stringUtils';



/**
 * Parses unit conversion Excel file with specific column requirements
 */
export async function parseUnitConversionFile(
  file: File
): Promise<UnitConversionResult> {
  const validationError = validateExcelFile(file);
  if (validationError) {
    throw validationError;
  }

  const parseResult = await parseExcelFile(file, {
    headerRow: 8,
    dataStartRow: 9,
    trimValues: true,
    skipEmptyRows: true,
  });


  const materialCodeHeader = parseResult.headers.find(header =>
    header.name.trim() === 'Mã vật tư' ||
    header.name.toLowerCase().includes('mã vật tư') ||
    header.name.toLowerCase().includes('mã')
  );

  const materialNameHeader = parseResult.headers.find(header =>
    header.name.trim() === 'Tên vật tư' ||
    header.name.toLowerCase().includes('tên vật tư') ||
    header.name.toLowerCase().includes('tên')
  );

  const unitHeader = parseResult.headers.find(header =>
    header.name.trim() === 'Đvt' ||
    header.name.toLowerCase().includes('đvt') ||
    header.name.toLowerCase().includes('đơn vị')
  );

  const conversionFactorHeader = parseResult.headers.find(header =>
    header.name.trim() === 'Hệ số' ||
    header.name.toLowerCase().includes('hệ số') ||
    header.name.toLowerCase().includes('factor')
  );


  if (!materialCodeHeader) {
    throw {
      type: 'MISSING_HEADER',
      message: 'Không tìm thấy cột "Mã vật tư"',
      details: `Các cột có sẵn: ${parseResult.headers.map(h => h.name).join(', ')}`
    } as UnitConversionError;
  }

  if (!materialNameHeader) {
    throw {
      type: 'MISSING_HEADER',
      message: 'Không tìm thấy cột "Tên vật tư"',
      details: `Các cột có sẵn: ${parseResult.headers.map(h => h.name).join(', ')}`
    } as UnitConversionError;
  }

  if (!unitHeader) {
    throw {
      type: 'MISSING_HEADER',
      message: 'Không tìm thấy cột "Đvt"',
      details: `Các cột có sẵn: ${parseResult.headers.map(h => h.name).join(', ')}`
    } as UnitConversionError;
  }

  if (!conversionFactorHeader) {
    throw {
      type: 'MISSING_HEADER',
      message: 'Không tìm thấy cột "Hệ số"',
      details: `Các cột có sẵn: ${parseResult.headers.map(h => h.name).join(', ')}`
    } as UnitConversionError;
  }


  const conversionData: UnitConversionData[] = [];

  for (const row of parseResult.rows) {
    const materialCode = row.values[materialCodeHeader.name];
    const materialName = row.values[materialNameHeader.name];
    const unit = row.values[unitHeader.name];
    const conversionFactor = row.values[conversionFactorHeader.name];

    // Skip rows with missing essential data
    if (!materialCode || !materialName || !unit || conversionFactor === undefined) {
      continue;
    }

    // Parse conversion factor as number
    const factor = typeof conversionFactor === 'number' 
      ? conversionFactor 
      : parseFloat(String(conversionFactor));

    if (isNaN(factor)) {
      continue;
    }

    conversionData.push({
      materialCode: String(materialCode).trim(),
      materialName: String(materialName).trim(),
      unit: standardizeUnitName(String(unit)),
      conversionFactor: factor,
      rowNumber: row.rowNumber,
    });
  }

  const localStorageData = conversionData.map(item => ({
    ma_vt: item.materialCode,
    dvt: item.unit, // Already standardized above
    he_so: item.conversionFactor
  }));

  try {
    localStorage.setItem('unit_conversion_data', JSON.stringify(localStorageData));
  } catch (error) {

  }

  return {
    data: conversionData,
    filename: file.name,
    dataRowCount: conversionData.length,
    metadata: {
      parsedAt: new Date(),
      fileSize: file.size,
      sheetName: parseResult.metadata.sheetName,
    },
  };
}

/**
 * Creates a lookup map for conversion factors by material code
 */
export function createConversionFactorMap(
  conversionData: UnitConversionData[]
): Map<string, number> {
  const map = new Map<string, number>();

  for (const item of conversionData) {
    map.set(item.materialCode, item.conversionFactor);
  }

  return map;
}

/**
 * Gets conversion factor for a specific material code
 */
export function getConversionFactor(
  materialCode: string,
  conversionData: UnitConversionData[]
): number | null {
  const item = conversionData.find(data =>
    data.materialCode.trim().toLowerCase() === materialCode.trim().toLowerCase()
  );
  return item ? item.conversionFactor : null;
}

/**
 * Gets conversion factor using the lookup map (more efficient for multiple lookups)
 */
export function getConversionFactorFromMap(
  materialCode: string,
  conversionMap: Map<string, number>
): number | null {
  // Try exact match first
  if (conversionMap.has(materialCode)) {
    return conversionMap.get(materialCode)!;
  }

  // Try case-insensitive match
  const lowerCode = materialCode.toLowerCase().trim();
  for (const [key, value] of conversionMap.entries()) {
    if (key.toLowerCase().trim() === lowerCode) {
      return value;
    }
  }

  return null;
}

/**
 * Validates if all material codes in a list have conversion factors
 */
export function validateMaterialCodes(
  materialCodes: string[],
  conversionData: UnitConversionData[]
): { valid: string[], missing: string[] } {
  const valid: string[] = [];
  const missing: string[] = [];

  for (const code of materialCodes) {
    const factor = getConversionFactor(code, conversionData);
    if (factor !== null) {
      valid.push(code);
    } else {
      missing.push(code);
    }
  }

  return { valid, missing };
}

// LocalStorage utility functions
export interface UnitConversionLocalStorage {
  ma_vt: string;
  dvt: string;
  he_so: number;
}

/**
 * Gets unit conversion data from localStorage
 */
export function getUnitConversionFromLocalStorage(): UnitConversionLocalStorage[] {
  try {
    const data = localStorage.getItem('unit_conversion_data');
    if (!data) return [];

    const parsed = JSON.parse(data);
    return Array.isArray(parsed) ? parsed : [];
  } catch (error) {
    return [];
  }
}

/**
 * Gets conversion factor for a material code from localStorage
 */
export function getConversionFactorFromLocalStorage(materialCode: string): number | null {
  const data = getUnitConversionFromLocalStorage();
  const item = data.find(item =>
    item.ma_vt.trim().toLowerCase() === materialCode.trim().toLowerCase()
  );
  return item ? item.he_so : null;
}

/**
 * Checks if unit conversion data exists in localStorage
 */
export function hasUnitConversionData(): boolean {
  const data = getUnitConversionFromLocalStorage();
  return data.length > 0;
}

/**
 * Clears unit conversion data from localStorage
 */
export function clearUnitConversionData(): void {
  try {
    localStorage.removeItem('unit_conversion_data');
  } catch (error) {

  }
}
