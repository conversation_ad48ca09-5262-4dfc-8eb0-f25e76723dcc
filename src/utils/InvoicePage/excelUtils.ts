import * as XLSX from "xlsx";
import type {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ExcelRow,
  ExcelParseResult,
  ExcelParseError,
  ExcelParseOptions,
} from "@/types/excel";
import type { InvoiceValidationResult } from "@/types/validation";

/**
 * Validates if a file is a supported Excel format
 */
export function validateExcelFile(file: File): ExcelParseError | null {
  // Check file extension
  const extension = file.name
    .toLowerCase()
    .substring(file.name.lastIndexOf("."));
  const supportedExtensions = [".xlsx", ".xls", ".xlsm"];

  if (!supportedExtensions.includes(extension)) {
    return {
      type: "INVALID_FORMAT",
      message: `Định dạng file không được hỗ trợ. Chỉ chấp nhận: ${supportedExtensions.join(
        ", "
      )}`,
      details: `File extension: ${extension}`,
    };
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    return {
      type: "FILE_READ_ERROR",
      message: "File quá lớn. Kích thước tối đa cho phép là 10MB",
      details: `File size: ${(file.size / 1024 / 1024).toFixed(2)}MB`,
    };
  }

  return null;
}

/**
 * Converts column number to Excel column letter (0 -> A, 1 -> B, etc.)
 */
export function numberToColumnLetter(num: number): string {
  let result = "";
  while (num >= 0) {
    result = String.fromCharCode(65 + (num % 26)) + result;
    num = Math.floor(num / 26) - 1;
  }
  return result;
}

/**
 * Extracts headers from the specified row
 */
function extractHeaders(
  worksheet: XLSX.WorkSheet,
  headerRow: number
): ExcelHeader[] {
  const headers: ExcelHeader[] = [];
  const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");

  for (let col = range.s.c; col <= range.e.c; col++) {
    const cellAddress = XLSX.utils.encode_cell({ r: headerRow - 1, c: col }); // Convert to 0-based
    const cell = worksheet[cellAddress];

    if (cell && cell.v !== undefined && cell.v !== null && cell.v !== "") {
      headers.push({
        index: col,
        name: String(cell.v).trim(),
        column: numberToColumnLetter(col),
      });
    }
  }

  return headers;
}

/**
 * Converts Excel serial date number to DD/MM/YYYY format
 */
function convertExcelDate(serialNumber: number): string {
  try {
    // Use XLSX built-in date conversion which handles Excel's leap year bug correctly
    const date = XLSX.SSF.parse_date_code(serialNumber);
    if (!date) {
      return serialNumber.toString();
    }

    const day = date.d.toString().padStart(2, '0');
    const month = date.m.toString().padStart(2, '0');
    const year = date.y;

    return `${day}/${month}/${year}`;
  } catch (error) {
    return serialNumber.toString(); // Return original value if conversion fails
  }
}

/**
 * Checks if a header name indicates a date column
 */
function isDateColumn(headerName: string): boolean {
  const dateKeywords = ['ngày', 'date', 'từ', 'xuất', 'hóa đơn'];
  const lowerHeaderName = headerName.toLowerCase();
  return dateKeywords.some(keyword => lowerHeaderName.includes(keyword));
}

/**
 * Extracts data rows starting from the specified row
 */
function extractDataRows(
  worksheet: XLSX.WorkSheet,
  headers: ExcelHeader[],
  dataStartRow: number,
  options: ExcelParseOptions
): ExcelRow[] {
  const rows: ExcelRow[] = [];
  const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");
  const maxRows = options.maxRows || range.e.r + 1;
  const endRow = Math.min(range.e.r, dataStartRow - 1 + maxRows);

  for (let rowNum = dataStartRow - 1; rowNum <= endRow; rowNum++) {
    // Convert to 0-based
    const rowData: Record<string, any> = {};
    const rowValues: Record<string, any> = {};
    let hasData = false;

    // Extract data for each header column
    for (const header of headers) {
      const cellAddress = XLSX.utils.encode_cell({
        r: rowNum,
        c: header.index,
      });
      const cell = worksheet[cellAddress];

      let value = cell?.v;

      // Process cell value
      if (value !== undefined && value !== null) {
        // Check if this is a date column and value is a number (Excel serial date)
        if (isDateColumn(header.name) && typeof value === 'number' && value > 1) {
          // Convert Excel serial date to DD/MM/YYYY format
          value = convertExcelDate(value);
        } else if (options.trimValues && typeof value === 'string') {
          value = value.trim();
        }
        hasData = true;
      } else {
        value = "";
      }

      rowData[header.column] = value;
      rowValues[header.name] = value;
    }

    // Skip empty rows if option is enabled
    if (options.skipEmptyRows && !hasData) {
      continue;
    }

    rows.push({
      rowNumber: rowNum + 1, // Convert back to 1-based for display
      data: rowData,
      values: rowValues,
    });
  }

  return rows;
}

/**
 * Parses an Excel file according to the specified format
 * Row 8: Headers, Row 9: Skip, Row 10+: Data
 */
export async function parseExcelFile(
  file: File,
  options: ExcelParseOptions = {}
): Promise<ExcelParseResult> {
  // Validate file first
  const validationError = validateExcelFile(file);
  if (validationError) {
    throw validationError;
  }

  try {
    // Set default options
    const parseOptions: Required<ExcelParseOptions> = {
      sheetIndex: 0,
      sheetName: "",
      headerRow: 8,
      dataStartRow: 10,
      maxRows: 0,
      trimValues: true,
      skipEmptyRows: true,
      ...options,
    };

    // Read file as array buffer
    const arrayBuffer = await file.arrayBuffer();

    // Parse workbook
    const workbook = XLSX.read(arrayBuffer, { type: "array" });

    if (!workbook.SheetNames.length) {
      throw {
        type: "PARSING_ERROR",
        message: "File Excel không chứa sheet nào",
      } as ExcelParseError;
    }

    // Get target sheet
    let sheetName: string;
    if (parseOptions.sheetName) {
      sheetName = parseOptions.sheetName;
      if (!workbook.SheetNames.includes(sheetName)) {
        throw {
          type: "PARSING_ERROR",
          message: `Không tìm thấy sheet "${sheetName}"`,
          details: `Available sheets: ${workbook.SheetNames.join(", ")}`,
        } as ExcelParseError;
      }
    } else {
      sheetName = workbook.SheetNames[parseOptions.sheetIndex];
      if (!sheetName) {
        throw {
          type: "PARSING_ERROR",
          message: `Không tìm thấy sheet tại index ${parseOptions.sheetIndex}`,
          details: `Available sheets: ${workbook.SheetNames.join(", ")}`,
        } as ExcelParseError;
      }
    }

    const worksheet = workbook.Sheets[sheetName];
    if (!worksheet) {
      throw {
        type: "PARSING_ERROR",
        message: `Không thể đọc sheet "${sheetName}"`,
      } as ExcelParseError;
    }

    // Extract headers from row 8
    const headers = extractHeaders(worksheet, parseOptions.headerRow);
    if (headers.length === 0) {
      throw {
        type: "MISSING_HEADER",
        message: `Không tìm thấy header tại dòng ${parseOptions.headerRow}`,
        rowNumber: parseOptions.headerRow,
      } as ExcelParseError;
    }

    // Extract data rows starting from row 10
    const rows = extractDataRows(
      worksheet,
      headers,
      parseOptions.dataStartRow,
      parseOptions
    );

    // Get total rows in sheet
    const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");
    const totalRows = range.e.r + 1;

    return {
      headers,
      rows,
      filename: file.name,
      totalRows,
      dataRowCount: rows.length,
      metadata: {
        parsedAt: new Date(),
        fileSize: file.size,
        sheetName,
      },
    };
  } catch (error) {
    // Handle different types of errors
    if (error && typeof error === "object" && "type" in error) {
      throw error as ExcelParseError;
    }

    throw {
      type: "PARSING_ERROR",
      message: "Lỗi không xác định khi đọc file Excel",
      details: error instanceof Error ? error.message : String(error),
    } as ExcelParseError;
  }
}

/**
 * Formats file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * A5 paper formatting configuration for Excel exports
 */
interface A5FormatConfig {
  // A5 dimensions: 148mm x 210mm (5.83" x 8.27")
  pageMargins: {
    left: number;
    right: number;
    top: number;
    bottom: number;
    header: number;
    footer: number;
  };
  fontSize: number;
  rowHeight: number;
  maxColumnWidth: number;
  orientation: "portrait" | "landscape";
}

const A5_FORMAT_CONFIG: A5FormatConfig = {
  pageMargins: {
    left: 0.5, // 0.5 inch margins for A5
    right: 0.5,
    top: 0.75,
    bottom: 0.75,
    header: 0.3,
    footer: 0.3,
  },
  fontSize: 9, // Smaller font for A5 readability
  rowHeight: 15, // Compact row height
  maxColumnWidth: 18, // Maximum column width for A5
  orientation: "portrait",
};

/**
 * Applies A5 paper formatting to an Excel worksheet
 */
function applyA5Formatting(worksheet: any): void {
  // Set page setup for A5 paper
  worksheet["!margins"] = A5_FORMAT_CONFIG.pageMargins;

  // Set page setup properties
  if (!worksheet["!pageSetup"]) {
    worksheet["!pageSetup"] = {};
  }

  worksheet["!pageSetup"] = {
    ...worksheet["!pageSetup"],
    paperSize: 11, // A5 paper size code
    orientation: A5_FORMAT_CONFIG.orientation,
    scale: 100, // 100% scaling
    fitToWidth: 1, // Fit to 1 page wide
    fitToHeight: 0, // Allow multiple pages vertically
    horizontalDpi: 300,
    verticalDpi: 300,
  };

  // Apply font formatting to all cells
  const range = XLSX.utils.decode_range(worksheet["!ref"] || "A1");
  for (let row = range.s.r; row <= range.e.r; row++) {
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
      if (worksheet[cellAddress]) {
        if (!worksheet[cellAddress].s) {
          worksheet[cellAddress].s = {};
        }
        worksheet[cellAddress].s.font = {
          name: "Arial",
          sz: A5_FORMAT_CONFIG.fontSize,
        };

        // Set row height
        if (!worksheet["!rows"]) {
          worksheet["!rows"] = [];
        }
        if (!worksheet["!rows"][row]) {
          worksheet["!rows"][row] = {};
        }
        worksheet["!rows"][row].hpt = A5_FORMAT_CONFIG.rowHeight;
      }
    }
  }
}

/**
 * Calculates optimal column widths for A5 paper based on data
 */
function calculateA5ColumnWidthsForData(
  headers: string[],
  data: any[]
): Array<{ wch: number }> {
  const availableWidth = 50; // Approximate usable width for A5 in character units

  // Calculate content-based widths
  const contentWidths = headers.map((header, index) => {
    // Start with header length
    let maxWidth = header.length;

    // Check data content lengths
    data.forEach((row) => {
      const cellValue = Array.isArray(row) ? row[index] : row[header];
      if (cellValue !== null && cellValue !== undefined) {
        const cellLength = String(cellValue).length;
        maxWidth = Math.max(maxWidth, cellLength);
      }
    });

    // Apply A5 constraints
    return Math.min(maxWidth + 2, A5_FORMAT_CONFIG.maxColumnWidth);
  });

  // If total width exceeds available, scale down proportionally
  const totalWidth = contentWidths.reduce((sum, w) => sum + w, 0);
  if (totalWidth > availableWidth) {
    const scaleFactor = availableWidth / totalWidth;
    return contentWidths.map((w) => ({
      wch: Math.max(6, Math.floor(w * scaleFactor)),
    }));
  }

  return contentWidths.map((w) => ({ wch: w }));
}

/**
 * Exports parsed data back to Excel format optimized for A5 paper printing
 */
export function exportToExcel(
  result: ExcelParseResult,
  filename?: string
): void {
  const worksheet = XLSX.utils.json_to_sheet(
    result.rows.map((row) => row.values),
    { header: result.headers.map((h) => h.name) }
  );

  // Apply A5-optimized column widths
  const columnWidths = calculateA5ColumnWidthsForData(
    result.headers.map((h) => h.name),
    result.rows.map((row) => row.values)
  );
  worksheet["!cols"] = columnWidths;

  // Apply A5 paper formatting
  applyA5Formatting(worksheet);

  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data");

  const exportFilename = filename || `parsed_${result.filename}`;
  XLSX.writeFile(workbook, exportFilename);
}

// Vietnamese Excel column headers for tax invoice validation export
const VIETNAMESE_EXCEL_HEADERS = [
  "Ký hiệu hoá đơn",
  "Số HD",
  "Ngày hoá đơn",
  "Mã cơ quan thuê",
  "Tên người bán",
  "MST người bán",
  "Địa chỉ người bán",
  "STK người bán",
  "Tên NH người bán",
  "Tên cty mua",
  "Mã số thuế cty mua",
  "Địa chỉ bên mua",
  "STT",
  "Tính chất",
  "Tên hàng hóa, dịch vụ",
  "Đơn vị tính",
  "Số lượng",
  "Đơn giá",
  "Chiết khấu",
  "Thuế suất",
  "Thành tiền chưa có thuế GTGT",
  "Thông tin khác",
  "KẾT QUẢ KIỂM TRA THÔNG TIN HOÁ ĐƠN",
  "KẾT QỦA KIỂM TRA THUẾ SUẤT",
] as const;

interface VietnameseInvoiceExcelRow {
  "Ký hiệu hoá đơn": string;
  "Số HD": string;
  "Ngày hoá đơn": string;
  "Mã cơ quan thuê": string;
  "Tên người bán": string;
  "MST người bán": string;
  "Địa chỉ người bán": string;
  "STK người bán": string;
  "Tên NH người bán": string;
  "Tên cty mua": string;
  "Mã số thuế cty mua": string;
  "Địa chỉ bên mua": string;
  STT: string;
  "Tính chất": string;
  "Tên hàng hóa, dịch vụ": string;
  "Đơn vị tính": string;
  "Số lượng": string;
  "Đơn giá": string;
  "Chiết khấu": string;
  "Thuế suất": string;
  "Thành tiền chưa có thuế GTGT": string;
  "Thông tin khác": string;
  "KẾT QUẢ KIỂM TRA THÔNG TIN HOÁ ĐƠN": string;
  "KẾT QỦA KIỂM TRA THUẾ SUẤT": string;
}

/**
 * Generates consolidated buyer validation result message
 * Returns empty string for valid results or when no validation rules match
 */
function generateBuyerValidationMessage(
  validationResult: InvoiceValidationResult
): string {
  if (validationResult.errors.length > 0) {
    return `LỖI: ${validationResult.errors.join("; ")}`;
  }

  if (validationResult.validationResults.length === 0) {
    // No validation rules configured - leave blank
    return "";
  }

  const invalidResults = validationResult.validationResults.filter(
    (r) => !r.isValid
  );
  if (invalidResults.length === 0) {
    // All validation passed - leave blank for clean output
    return "";
  }

  const errorMessages = invalidResults.map((r) => `${r.field}: ${r.message}`);
  return `KHÔNG HỢP LỆ: ${errorMessages.join("; ")}`;
}

/**
 * Generates tax rate validation result message for a specific line item
 * Returns empty string for valid results or when no validation rules match
 */
function generateTaxRateValidationMessage(
  validationResult: InvoiceValidationResult,
  lineItemIndex: number
): string {
  if (!validationResult.taxRateValidationResults) {
    // No tax rate validation configured - leave blank
    return "";
  }

  const taxResult = validationResult.taxRateValidationResults.find(
    (r) => r.lineItemIndex === lineItemIndex
  );
  if (!taxResult) {
    // No tax rate data for this line item - leave blank
    return "";
  }

  if (taxResult.isValid) {
    // Valid tax rate - leave blank for clean output
    return "";
  }

  // Filter out validation errors containing "không chứa" (does not contain)
  if (taxResult.message.includes("không chứa")) {
    return "";
  }

  return `KHÔNG HỢP LỆ: ${taxResult.message}`;
}

/**
 * Exports Vietnamese tax invoice validation results to Excel format
 * Creates invoice-level rows with line item expansion and validation results
 * Filters results based on selected company if provided
 */
export function exportVietnameseInvoiceValidationToExcel(
  validationResults: InvoiceValidationResult[],
  filename?: string,
  selectedCompany?: string | null
): void {
  const excelRows: VietnameseInvoiceExcelRow[] = [];

  // Filter validation results based on selected company
  const filteredResults = selectedCompany
    ? validationResults.filter(
        (result) => result.extractedData.buyerTaxCode === selectedCompany
      )
    : validationResults;

  filteredResults.forEach((validationResult) => {
    const { extractedData } = validationResult;
    const buyerValidationMessage =
      generateBuyerValidationMessage(validationResult);

    // If no line items, create one row with invoice-level data
    if (!extractedData.lineItems || extractedData.lineItems.length === 0) {
      excelRows.push({
        "Ký hiệu hoá đơn": extractedData.invoiceSerial || "",
        "Số HD": extractedData.invoiceNumber || "",
        "Ngày hoá đơn": extractedData.invoiceDate || "",
        "Mã cơ quan thuê": extractedData.taxAuthorityCode || "",
        "Tên người bán": extractedData.sellerName || "",
        "MST người bán": extractedData.sellerTaxCode || "",
        "Địa chỉ người bán": extractedData.sellerAddress || "",
        "STK người bán": extractedData.sellerBankAccount || "",
        "Tên NH người bán": extractedData.sellerBankName || "",
        "Tên cty mua": extractedData.buyerName || "",
        "Mã số thuế cty mua": extractedData.buyerTaxCode || "",
        "Địa chỉ bên mua": extractedData.buyerAddress || "",
        STT: "",
        "Tính chất": "",
        "Tên hàng hóa, dịch vụ": "",
        "Đơn vị tính": "",
        "Số lượng": "",
        "Đơn giá": "",
        "Chiết khấu": "",
        "Thuế suất": "",
        "Thành tiền chưa có thuế GTGT": "",
        "Thông tin khác": "",
        "KẾT QUẢ KIỂM TRA THÔNG TIN HOÁ ĐƠN": buyerValidationMessage,
        "KẾT QỦA KIỂM TRA THUẾ SUẤT": "", // Leave blank for invoices without line items
      });
      return;
    }

    // Create rows for each line item with repeated invoice header information
    extractedData.lineItems.forEach((lineItem, index) => {
      const taxRateValidationMessage = generateTaxRateValidationMessage(
        validationResult,
        index
      );

      excelRows.push({
        "Ký hiệu hoá đơn": extractedData.invoiceSerial || "",
        "Số HD": extractedData.invoiceNumber || "",
        "Ngày hoá đơn": extractedData.invoiceDate || "",
        "Mã cơ quan thuê": extractedData.taxAuthorityCode || "",
        "Tên người bán": extractedData.sellerName || "",
        "MST người bán": extractedData.sellerTaxCode || "",
        "Địa chỉ người bán": extractedData.sellerAddress || "",
        "STK người bán": extractedData.sellerBankAccount || "",
        "Tên NH người bán": extractedData.sellerBankName || "",
        "Tên cty mua": extractedData.buyerName || "",
        "Mã số thuế cty mua": extractedData.buyerTaxCode || "",
        "Địa chỉ bên mua": extractedData.buyerAddress || "",
        STT: lineItem.stt?.toString() || "",
        "Tính chất": "Hàng hóa dịch vụ",
        "Tên hàng hóa, dịch vụ": lineItem.itemName || "",
        "Đơn vị tính": lineItem.unit || "",
        "Số lượng": lineItem.quantity?.toString() || "",
        "Đơn giá": lineItem.unitPrice?.toString() || "",
        "Chiết khấu":
          lineItem.discountAmount?.toString() ||
          lineItem.discountRate?.toString() ||
          "",
        "Thuế suất": lineItem.taxRate?.toString() || "",
        "Thành tiền chưa có thuế GTGT": lineItem.amount?.toString() || "",
        "Thông tin khác": "",
        "KẾT QUẢ KIỂM TRA THÔNG TIN HOÁ ĐƠN": buyerValidationMessage,
        "KẾT QỦA KIỂM TRA THUẾ SUẤT": taxRateValidationMessage,
      });
    });
  });

  // Create worksheet with Vietnamese headers
  const worksheet = XLSX.utils.json_to_sheet(excelRows, {
    header: [...VIETNAMESE_EXCEL_HEADERS],
  });

  // Set A5-optimized column widths for Vietnamese invoice validation
  const columnWidths = [
    { wch: 10 }, // Ký hiệu hoá đơn
    { wch: 8 }, // Số HD
    { wch: 10 }, // Ngày hoá đơn
    { wch: 10 }, // Mã cơ quan thuê
    { wch: 18 }, // Tên người bán
    { wch: 12 }, // MST người bán
    { wch: 20 }, // Địa chỉ người bán
    { wch: 12 }, // STK người bán
    { wch: 15 }, // Tên NH người bán
    { wch: 18 }, // Tên cty mua
    { wch: 12 }, // Mã số thuế cty mua
    { wch: 20 }, // Địa chỉ bên mua
    { wch: 6 }, // STT
    { wch: 12 }, // Tính chất
    { wch: 20 }, // Tên hàng hóa, dịch vụ
    { wch: 8 }, // Đơn vị tính
    { wch: 8 }, // Số lượng
    { wch: 10 }, // Đơn giá
    { wch: 8 }, // Chiết khấu
    { wch: 8 }, // Thuế suất
    { wch: 12 }, // Thành tiền chưa có thuế GTGT
    { wch: 10 }, // Thông tin khác
    { wch: 25 }, // KẾT QUẢ KIỂM TRA THÔNG TIN HOÁ ĐƠN
    { wch: 25 }, // KẾT QỦA KIỂM TRA THUẾ SUẤT
  ];

  worksheet["!cols"] = columnWidths;

  // Apply A5 paper formatting
  applyA5Formatting(worksheet);

  // Create workbook and add worksheet
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, "Kết quả kiểm tra hóa đơn");

  // Generate Vietnamese filename with current date
  const currentDate = new Date().toISOString().split("T")[0].replace(/-/g, "");
  const exportFilename =
    filename || `Ket_qua_kiem_tra_hoa_don_${currentDate}.xlsx`;

  // Export to Excel file
  XLSX.writeFile(workbook, exportFilename);
}
