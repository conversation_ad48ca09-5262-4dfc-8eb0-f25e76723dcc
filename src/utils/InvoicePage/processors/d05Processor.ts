import * as XLSX from 'xlsx';
import { createGroupKey } from '../helpers/grouping';
import { loadHTLookupData } from '../helpers/htLookup';
import type { D05Record, ProcessedRecord } from '../types';

export interface ARITORecord {
  'Mã đơn vị': string;
  '<PERSON><PERSON> chứng từ*': string;
  'Ngày hạch toán*': string;
  'Ngoại tệ*': string;
  'Tỷ giá': string;
  'Trạng thái HĐĐT*': number;
  'Số hóa đơn': string;
  'Ngày hóa đơn': string;
  'Ký hiệu': string;
  'Mẫu hóa đơn': string;
  'Số phiếu xuất': string;
  '<PERSON><PERSON>y xuất kho': string;
  'Mã khách hàng*': string;
  'Người nhận': string;
  'Mã nhân viên': string;
  'Tài khoản nợ*': string;
  '<PERSON><PERSON><PERSON> gi<PERSON>': string;
  '<PERSON><PERSON><PERSON> d<PERSON>*': string;
  'Chuyển dữ liệu': string;
  '<PERSON><PERSON> sản phẩm*': string;
  'Tên sản phẩm': string;
  'Đvt': string;
  '<PERSON>ã kho*': string;
  'Mã lô': string;
  'Mã vị trí': string;
  'Loại hàng': number;
  'Số lượng': number;
  'Giá bán': number;
  'Thành tiền': number;
  'Tl ck(%)': string;
  'Ch.khấu': number;
  'Đích danh': string;
  'Giá tồn': string;
  'Tiền': string;
  'Thuế suất*': string;
  'Tk thuế có*': string;
  'Thuế': string;
  'Tk doanh thu*': string;
  'Tk giá vốn*': string;
  'Tk kho': string;
  'Tk chiết khấu': string;
  'Tk khuyến mãi': string;
  'Ghi chú tên vật tư': string;
  'Bộ phận': string;
  'Vụ việc': string;
  'Hợp đồng': string;
  'Đợt thanh toán': string;
  'Khế ước': string;
  'Phí': string;
  'Sản phẩm': string;
  'Lệnh sản xuất': string;
}

/**
 * Converts ProcessedRecord to ARITO format with default values
 */
export function convertToARITO(processedData: ProcessedRecord[]): ARITORecord[] {
  return processedData.map(record => {
    const aritoRecord: ARITORecord = {
      'Mã đơn vị': '0317749937 ',
      'Số chứng từ*': record['số chứng từ '] || '',
      'Ngày hạch toán*': record['Ngày'] || '',
      'Ngoại tệ*': 'VND ',
      'Tỷ giá': '',
      'Trạng thái HĐĐT*': 0,
      'Số hóa đơn': '',
      'Ngày hóa đơn': record['Ngày'] || '',
      'Ký hiệu': '',
      'Mẫu hóa đơn': '',
      'Số phiếu xuất': '',
      'Ngày xuất kho': record['Ngày'] || '',
      'Mã khách hàng*': 'kl',
      'Người nhận': '',
      'Mã nhân viên': '',
      'Tài khoản nợ*': String(record['tk nợ'] || ''),
      'Diễn giải': record['PTTT'] || '',
      'Giao dịch*': 'PO. Bán hàng POS',
      'Chuyển dữ liệu': '',
      'Mã sản phẩm*': record['Mã hàng'] || '',
      'Tên sản phẩm': record['Tên hàng'] || '',
      'Đvt': record['Đơn vị tính'] || '',
      'Mã kho*': 'sk-006 ',
      'Mã lô': '',
      'Mã vị trí': '',
      'Loại hàng': 0,
      'Số lượng': record['Sum of Số lượng'] || 0,
      'Giá bán': record['Giá bán'] || 0,
      'Thành tiền': record['Sum of Thành tiền'] || 0,
      'Tl ck(%)': '',
      'Ch.khấu': record['tổng giảm giá ck'] || 0,
      'Đích danh': '',
      'Giá tồn': '',
      'Tiền': '',
      'Thuế suất*': 'Không kê khai thuế',
      'Tk thuế có*': '33311',
      'Thuế': '',
      'Tk doanh thu*': '5112',
      'Tk giá vốn*': '63221 ',
      'Tk kho': '1552 ',
      'Tk chiết khấu': '52111 ',
      'Tk khuyến mãi': '',
      'Ghi chú tên vật tư': '',
      'Bộ phận': 'sk006',
      'Vụ việc': '',
      'Hợp đồng': '',
      'Đợt thanh toán': '',
      'Khế ước': '',
      'Phí': '',
      'Sản phẩm': '',
      'Lệnh sản xuất': ''
    };

    return aritoRecord;
  });
}

// Note: ProcessedRecord is now imported from types.ts

/**
 * Processes D05-IPOS Excel file according to requirements
 */
export async function processD05File(file: File): Promise<ProcessedRecord[]> {
  return new Promise(async (resolve, reject) => {
    try {
      const htLookup = await loadHTLookupData(file);

      const reader = new FileReader();
    
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });

          const d05SheetName = workbook.SheetNames.find(name => {
            const normalized = name.toLowerCase().replace(/\s+/g, '').replace(/-/g, '');
            return normalized === 'd05ipos';
          });

          if (!d05SheetName) {
            throw new Error('Không tìm thấy sheet "D05-ipos" trong file Excel');
          }

          const worksheet = workbook.Sheets[d05SheetName];

          const jsonData = XLSX.utils.sheet_to_json(worksheet, {
            range: 1
          }) as D05Record[];

          if (jsonData.length === 0) {
            throw new Error('File Excel không có dữ liệu');
          }
          
          const groupedData = new Map<string, ProcessedRecord>();
          
          jsonData.forEach(record => {
            if (!record['Ngày'] || !record['Cửa hàng'] || !record['PTTT'] ||
                !record['Mã hàng']) {
              return;
            }

            const key = createGroupKey(record);

            if (groupedData.has(key)) {
              const existing = groupedData.get(key)!;

              existing['Sum of Số lượng'] += Number(record['Số lượng']) || 0;
              existing['Sum of Thành tiền'] += Number(record['Thành tiền']) || 0;
              existing['Sum of Giảm giá'] += Number(record['Giảm giá']) || 0;
              existing['Sum of Chiết khấu'] += Number(record['Chiết khấu']) || 0;
              existing['Sum of Phiếu giảm giá'] += Number(record['Phiếu giảm giá']) || 0;

              existing['Giá bán'] = existing['Sum of Số lượng'] > 0
                ? existing['Sum of Thành tiền'] / existing['Sum of Số lượng']
                : 0;

              existing['tổng giảm giá ck'] = existing['Sum of Giảm giá'] + existing['Sum of Chiết khấu'];
            } else {
              const quantity = Number(record['Số lượng']) || 0;
              const totalAmount = Number(record['Thành tiền']) || 0;
              const discount = Number(record['Giảm giá']) || 0;
              const rebate = Number(record['Chiết khấu']) || 0;
              const voucherDiscount = Number(record['Phiếu giảm giá']) || 0;

              const lookupKey = `${record['Cửa hàng']}_${record['PTTT']}`;
              const lookupData = htLookup.get(lookupKey);

              const storeCode = lookupData?.storeCode || 'sk-006';
              const docNumber = `${record['Ngày']}${storeCode}${lookupData?.accountCode || 1314}`;

              const newRecord: any = {
                'Ngày': record['Ngày'] || '',
                'Cửa hàng': record['Cửa hàng'] || '',
                'PTTT': record['PTTT'] || '',
                'Mã hàng': record['Mã hàng'] || '',
                'Tên hàng': record['Tên hàng'] || '',
                'Đơn vị tính': record['Đơn vị tính'] || '',
                'Giá bán': quantity > 0 ? totalAmount / quantity : 0,
                'Sum of Số lượng': quantity,
                'Sum of Thành tiền': totalAmount,
                'Sum of Giảm giá': discount,
                'Sum of Chiết khấu': rebate,
                'Sum of Phiếu giảm giá': voucherDiscount,
                'ch&pttt': `${record['Cửa hàng'] || ''}${record['PTTT'] || ''}`,
                'số chứng từ ': docNumber,
                'mã kho': lookupData?.storeCode || 'sk-006',
                'mã bộ phận': lookupData?.departmentCode || 'sk006',
                'tk nợ': lookupData?.accountCode || 1314,
                'tổng giảm giá ck': discount + rebate,
                'tk ck': 52111
              };

              // Add empty column (column 12) - will be handled in download function

              groupedData.set(key, newRecord);
            }
          });



          const processedData = Array.from(groupedData.values());


          processedData.sort((a, b) => {
            if (a['Ngày'] !== b['Ngày']) return a['Ngày'].localeCompare(b['Ngày']);
            if (a['Cửa hàng'] !== b['Cửa hàng']) return a['Cửa hàng'].localeCompare(b['Cửa hàng']);
            if (a['PTTT'] !== b['PTTT']) return a['PTTT'].localeCompare(b['PTTT']);
            return a['Mã hàng'].localeCompare(b['Mã hàng']);
          });

          const summaryRows = processedData.filter(row =>
            row['Ngày'] === '-' || row['Cửa hàng'] === 'Tổng'
          );

          if (summaryRows.length > 0) {
            const filteredData = processedData.filter(row =>
              row['Ngày'] !== '-' && row['Cửa hàng'] !== 'Tổng'
            );
            processedData.length = 0;
            processedData.push(...filteredData);
          }
          
          resolve(processedData);
        } catch (error) {
          reject(new Error(`Lỗi xử lý dữ liệu: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
      };

      reader.onerror = () => {
        reject(new Error('Lỗi đọc file'));
      };

      reader.readAsArrayBuffer(file);
    } catch (error) {
      reject(new Error(`Lỗi xử lý file: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  });
}
