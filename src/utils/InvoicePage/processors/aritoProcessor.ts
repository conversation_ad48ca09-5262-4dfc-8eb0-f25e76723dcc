import type { ProcessedRecord } from '../types';
import { getCustomerCodeFromMapping } from '../helpers/paymentMethodMapping';

/**
 * ARITO data structure based on actual test file format
 */
export interface ARITORecord {
  /** Mã đơn vị */
  'Mã đơn vị': string;
  /** <PERSON><PERSON> chứng từ* */
  'Số chứng từ*': string;
  /** <PERSON><PERSON>y hạch toán* */
  'Ngày hạch toán*': string;
  /** Ngoại tệ* */
  'Ngoại tệ*': string;
  /** Tỷ giá */
  'Tỷ giá': string | number;
  /** Trạng thái HĐĐT* */
  'Trạng thái HĐĐT*': number;
  /** Số hóa đơn */
  'Số hóa đơn': string;
  /** Ngày hóa đơn */
  'Ngày hóa đơn': string;
  /** Ký hiệu */
  'Ký hiệu': string;
  /** Mẫu hóa đơn */
  'Mẫu hóa đơn': string;
  /** <PERSON><PERSON> phiếu xuất */
  'S<PERSON> phiếu xuất': string;
  /** <PERSON><PERSON>y xuất kho */
  'Ngày xuất kho': string;
  /** Mã khách hàng* */
  'Mã khách hàng*': string | number;
  /** Người nhận */
  'Người nhận': string;
  /** Mã nhân viên */
  'Mã nhân viên': string;
  /** Tài khoản nợ* */
  'Tài khoản nợ*': number;
  /** Diễn giải */
  'Diễn giải': string;
  /** Giao dịch* */
  'Giao dịch*': string;
  /** Chuyển dữ liệu */
  'Chuyển dữ liệu': string;
  /** Mã sản phẩm* */
  'Mã sản phẩm*': string;
  /** Tên sản phẩm */
  'Tên sản phẩm': string;
  /** Đvt */
  'Đvt': string;
  /** Mã kho* */
  'Mã kho*': string;
  /** Mã lô */
  'Mã lô': string;
  /** Mã vị trí */
  'Mã vị trí': string;
  /** Loại hàng */
  'Loại hàng': string;
  /** Số lượng */
  'Số lượng': number;
  /** Giá bán */
  'Giá bán ': number;
  /** Thành tiền */
  'Thành tiền ': number;
  /** Tl ck(%) */
  'Tl ck(%)': number;
  /** Ch.khấu */
  'Ch.khấu ': number;
  /** Đích danh */
  'Đích danh': string;
  /** Giá tồn */
  'Giá tồn ': number;
  /** Tiền */
  'Tiền ': number;
  /** Thuế suất* */
  'Thuế suất*': string;
  /** Tk thuế có* */
  'Tk thuế có*': string;
  /** Thuế */
  'Thuế ': string;
  /** Tk doanh thu* */
  'Tk doanh thu*': string;
  /** Tk giá vốn* */
  'Tk giá vốn*': number;
  /** Tk kho */
  'Tk kho': string;
  /** Tk chiết khấu */
  'Tk chiết khấu': number;
  /** Tk khuyến mãi */
  'Tk khuyến mãi': string;
  /** Ghi chú tên vật tư */
  'Ghi chú tên vật tư': string;
  /** Bộ phận */
  'Bộ phận': string;
  /** Vụ việc */
  'Vụ việc': string;
  /** Hợp đồng */
  'Hợp đồng': string;
  /** Đợt thanh toán */
  'Đợt thanh toán': string;
  /** Khế ước */
  'Khế ước': string;
  /** Phí */
  'Phí': string;
  /** Sản phẩm */
  'Sản phẩm': string;
  /** Lệnh sản xuất */
  'Lệnh sản xuất': string;
}

/**
 * Process ARITO data using the result from D05 processing
 * @param d05ProcessedData - The processed D05 data
 * @param paymentMapping - Required payment method to customer code mapping
 * @returns Promise<ARITORecord[]> - The processed ARITO data
 */
export async function processARITOFile(
  d05ProcessedData: ProcessedRecord[],
  paymentMapping: Map<string, string>
): Promise<ARITORecord[]> {
  try {
    const aritoData: ARITORecord[] = d05ProcessedData.map((record) => {
      const safeParseFloat = (value: any, defaultValue: number = 0): number => {
        if (value === undefined || value === null || value === '') return defaultValue;
        const parsed = parseFloat(String(value));
        return isNaN(parsed) ? defaultValue : parsed;
      };

      return {
        'Mã đơn vị': '0317749937', // Default company code
        'Số chứng từ*': record['số chứng từ '] || '',
        'Ngày hạch toán*': record['Ngày'] || '',
        'Ngoại tệ*': 'VND',
        'Tỷ giá': '',
        'Trạng thái HĐĐT*': 0,
        'Số hóa đơn': '',
        'Ngày hóa đơn': record['Ngày'] || '',
        'Ký hiệu': '',
        'Mẫu hóa đơn': '',
        'Số phiếu xuất': '',
        'Ngày xuất kho': record['Ngày'] || '',
        'Mã khách hàng*': (() => {
          const customerCode = getCustomerCodeFromMapping(
            record['PTTT'] || '',
            paymentMapping
          );
          return customerCode;
        })(),
        'Người nhận': record['Cửa hàng'] || '',
        'Mã nhân viên': '',
        'Tài khoản nợ*': safeParseFloat(record['tk nợ']),
        'Diễn giải': ' PO. Bán hàng POS',
        'Giao dịch*': '',
        'Chuyển dữ liệu': '',
        'Mã sản phẩm*': record['Mã hàng'] || '',
        'Tên sản phẩm': record['Tên hàng'] || '',
        'Đvt': record['Đơn vị tính'] || '',
        'Mã kho*': record['mã kho'] || '',
        'Mã lô': '',
        'Mã vị trí': '',
        'Loại hàng': '0',
        'Số lượng': safeParseFloat(record['Sum of Số lượng']),
        'Giá bán ': record['Giá bán'] || 0,
        'Thành tiền ': record['Sum of Thành tiền'] || 0,
        'Tl ck(%)': 0,
        'Ch.khấu ': safeParseFloat(record['Sum of Chiết khấu']),
        'Đích danh': '',
        'Giá tồn ': 0,
        'Tiền ': 0,
        'Thuế suất*': 'Không kê khai thuế',
        'Tk thuế có*': '33311',
        'Thuế ': record['Thuế'] || '',
        'Tk doanh thu*': '5112',
        'Tk giá vốn*': 63221,
        'Tk kho': '1552',
        'Tk chiết khấu': safeParseFloat(record['tk ck'], 52111),
        'Tk khuyến mãi': '',
        'Ghi chú tên vật tư': '',
        'Bộ phận': record['mã bộ phận'] || '',
        'Vụ việc': '',
        'Hợp đồng': '',
        'Đợt thanh toán': '',
        'Khế ước': '',
        'Phí': record['Phí'] || '',
        'Sản phẩm': '',
        'Lệnh sản xuất': ''
      };
    });

    return aritoData;

  } catch (error) {
    throw new Error(`Lỗi xử lý dữ liệu ARITO: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
