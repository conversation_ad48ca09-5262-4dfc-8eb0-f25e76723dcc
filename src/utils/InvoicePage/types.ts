export interface D05Record {
  'Ngày': string;
  'Cửa hàng': string;
  'PTTT': string;
  'Mã hàng': string;
  'Tên hàng': string;
  'Đơn vị tính': string;
  'Số lượng': number;
  '<PERSON><PERSON><PERSON> bán': number;
  'Thành tiền': number;
  'Giảm giá': number;
  'Chiết khấu': number;
  'Phiếu giảm giá'?: number;
  'Số khách'?: string;
}

export interface ProcessedRecord {
  'Ngày': string;
  'Cửa hàng': string;
  'PTTT': string;
  'Mã hàng': string;
  'Tên hàng': string;
  'Đơn vị tính': string;
  'Gi<PERSON> bán': number;
  'Sum of Số lượng': number;
  'Sum of Thành tiền': number;
  'Sum of Giảm giá': number;
  'Sum of Chiết khấu': number;
  'Sum of Phiếu giảm giá': number;
  [key: string]: any;
  'ch&pttt': string;
  'số chứng từ ': string;
  'mã kho': string;
  'mã bộ phận': string;
  'tk nợ': number;
  'tổng giảm giá ck': number;
  'tk ck': number;
}

export interface HTLookupRecord {
  store: string;
  paymentMethod: string;
  storeCode: string;
  departmentCode: string;
  accountCode: number;
}

export interface PaymentMethodMapping {
  paymentMethod: string;
  customerCode: string;
}
