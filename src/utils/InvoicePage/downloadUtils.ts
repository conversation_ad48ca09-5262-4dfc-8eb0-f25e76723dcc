import * as XLSX from 'xlsx';
import type { ProcessedRecord } from './types';
import { convertToARITO, type ARITORecord } from './processors/d05Processor';

export function downloadProcessedData(processedData: ProcessedRecord[], filename: string): void {
  const newWorkbook = XLSX.utils.book_new();

  const headers = [
    'Ngày',
    'Cử<PERSON> hàng',
    'PTTT',
    'Mã hàng',
    'Tên hàng',
    'Đơn vị tính',
    '<PERSON><PERSON><PERSON> bán',
    'Sum of Số lượng',
    'Sum of Thành tiền',
    'Sum of Giảm giá',
    'Sum of Chiết khấu',
    'Sum of Phiếu giảm giá',
    undefined, // Empty column
    'ch&pttt',
    'số chứng từ ',
    'mã kho',
    'mã bộ phận',
    'tk nợ',
    'tổng giảm giá ck',
    'tk ck'
  ];

  const newWorksheet = XLSX.utils.aoa_to_sheet([]);

  // Row 1: Title row
  const titleRow = ['D05 THEO NGÀY & CỬA HÀNG &PTTT& MÃ HÀNG'];
  while (titleRow.length < headers.length) {
    titleRow.push('');
  }
  XLSX.utils.sheet_add_aoa(newWorksheet, [titleRow], { origin: 'A1' });

  // Row 2: Empty row
  const emptyRow = new Array(headers.length).fill('');
  XLSX.utils.sheet_add_aoa(newWorksheet, [emptyRow], { origin: 'A2' });

  // Row 3: Headers
  XLSX.utils.sheet_add_aoa(newWorksheet, [headers], { origin: 'A3' });

  // Row 4+: Data rows
  const dataArray = processedData.map(row =>
    headers.map(header => {
      if (header === undefined) {
        return undefined;
      }

      const value = row[header as keyof ProcessedRecord];

      // For numeric fields, default to 0 instead of empty
      const numericFields = [
        'Giá bán',
        'Sum of Số lượng',
        'Sum of Thành tiền',
        'Sum of Giảm giá',
        'Sum of Chiết khấu',
        'Sum of Phiếu giảm giá',
        'tk nợ',
        'tổng giảm giá ck',
        'tk ck'
      ];

      if (numericFields.includes(header as string)) {
        // For numeric fields: return 0 if undefined/null/empty
        return (value !== undefined && value !== null && value !== '') ? value : 0;
      } else {
        // For string fields: return empty string if undefined/null
        return (value !== undefined && value !== null) ? value : '';
      }
    })
  );

  XLSX.utils.sheet_add_aoa(newWorksheet, dataArray, { origin: 'A4' });

  XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, 'XU_LY_D05');

  // Download file
  XLSX.writeFile(newWorkbook, filename);
}

/**
 * Downloads processed data in ARITO format
 */
export function downloadARITOData(processedData: ProcessedRecord[], filename: string): void {
  // Convert processed data to ARITO format
  const aritoData = convertToARITO(processedData);

  const newWorkbook = XLSX.utils.book_new();

  // Define ARITO headers in exact order
  const headers: (keyof ARITORecord)[] = [
    'Mã đơn vị',
    'Số chứng từ*',
    'Ngày hạch toán*',
    'Ngoại tệ*',
    'Tỷ giá',
    'Trạng thái HĐĐT*',
    'Số hóa đơn',
    'Ngày hóa đơn',
    'Ký hiệu',
    'Mẫu hóa đơn',
    'Số phiếu xuất',
    'Ngày xuất kho',
    'Mã khách hàng*',
    'Người nhận',
    'Mã nhân viên',
    'Tài khoản nợ*',
    'Diễn giải',
    'Giao dịch*',
    'Chuyển dữ liệu',
    'Mã sản phẩm*',
    'Tên sản phẩm',
    'Đvt',
    'Mã kho*',
    'Mã lô',
    'Mã vị trí',
    'Loại hàng',
    'Số lượng',
    'Giá bán',
    'Thành tiền',
    'Tl ck(%)',
    'Ch.khấu',
    'Đích danh',
    'Giá tồn',
    'Tiền',
    'Thuế suất*',
    'Tk thuế có*',
    'Thuế',
    'Tk doanh thu*',
    'Tk giá vốn*',
    'Tk kho',
    'Tk chiết khấu',
    'Tk khuyến mãi',
    'Ghi chú tên vật tư',
    'Bộ phận',
    'Vụ việc',
    'Hợp đồng',
    'Đợt thanh toán',
    'Khế ước',
    'Phí',
    'Sản phẩm',
    'Lệnh sản xuất'
  ];

  // Create worksheet with headers
  const newWorksheet = XLSX.utils.aoa_to_sheet([headers]);

  // Add data rows
  const dataArray = aritoData.map(row =>
    headers.map(header => {
      const value = row[header];
      return value !== undefined && value !== null ? value : '';
    })
  );

  XLSX.utils.sheet_add_aoa(newWorksheet, dataArray, { origin: 'A2' });

  XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, 'ARITO_DATA');

  // Download file
  XLSX.writeFile(newWorkbook, filename);
}

/**
 * Downloads ARITO data directly (already converted)
 */
export function downloadDirectARITOData(aritoData: any[], filename: string): void {
  const newWorkbook = XLSX.utils.book_new();

  // Create headers for ARITO format
  const headers = Object.keys(aritoData[0] || {});

  // Create worksheet
  const newWorksheet = XLSX.utils.aoa_to_sheet([headers]);

  // Convert ARITO data to array format
  const dataArray = aritoData.map(record =>
    headers.map(header => {
      const value = record[header];
      // Show 0 for price fields if no data
      if ((header === 'Giá bán ' || header === 'Thành tiền ') && (value === null || value === undefined || value === '')) {
        return 0;
      }
      // Show empty cell for inventory price and money fields
      if ((header === 'Giá tồn ' || header === 'Tiền ') && (value === 0 || value === null || value === undefined || value === '')) {
        return undefined;
      }
      return value !== undefined && value !== null ? value : '';
    })
  );

  XLSX.utils.sheet_add_aoa(newWorksheet, dataArray, { origin: 'A2' });

  XLSX.utils.book_append_sheet(newWorkbook, newWorksheet, 'ARITO_DATA');

  // Download file
  XLSX.writeFile(newWorkbook, filename);
}
