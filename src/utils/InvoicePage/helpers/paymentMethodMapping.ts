import * as XLSX from 'xlsx';

/**
 * Loads payment method to customer code mapping from Gộp đơn Excel file
 * Expected structure:
 * - Row 1: Headers (Company, Payment Method, Customer Code)
 * - Row 2: Skip this row (ignore during processing)
 * - Row 3: Data mapping (TTM, BE-DHI, BE)
 */
export async function loadPaymentMethodMapping(file: File): Promise<Map<string, string>> {
  try {
    const arrayBuffer = await file.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });

    const firstSheetName = workbook.SheetNames[0];
    if (!firstSheetName) {
      throw new Error('File Excel không có sheet nào');
    }

    const worksheet = workbook.Sheets[firstSheetName];

    const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];

    if (rawData.length < 3) {
      throw new Error('File Excel phải có ít nhất 3 dòng (header, skip, data)');
    }

    const mappingMap = new Map<string, string>();

    for (let i = 2; i < rawData.length; i++) {
      const mappingRow = rawData[i];

      if (!mappingRow || mappingRow.length < 3) continue;

      const paymentMethod = String(mappingRow[1] || '').trim();
      const customerCode = String(mappingRow[2] || '').trim();

      if (paymentMethod && customerCode) {
        mappingMap.set(paymentMethod, customerCode);
        console.log(`Loaded mapping: ${paymentMethod} → ${customerCode}`);
      }
    }

    if (mappingMap.size === 0) {
      throw new Error('Không tìm thấy dữ liệu mapping hợp lệ trong file');
    }

    console.log(`Loaded ${mappingMap.size} payment method mappings`);
    return mappingMap;

  } catch (error) {
    console.error('Error loading payment method mapping:', error);
    throw new Error(`Lỗi đọc file mapping: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Applies payment method mapping to get customer code
 */
export function getCustomerCodeFromMapping(
  paymentMethod: string,
  mapping: Map<string, string>
): string {
  if (!paymentMethod) return '';

  return mapping.get(paymentMethod) || '';
}
