import type { D05Record } from '../types';

/**
 * Creates a unique key for grouping records
 * Group by: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> hàng, PTTT, <PERSON><PERSON> hàng, <PERSON><PERSON><PERSON> b<PERSON>
 * Include <PERSON><PERSON><PERSON> bán to avoid grouping records with different prices
 */
export function createGroupKey(record: D05Record): string {
  const giaBan = Number(record['Giá bán']) || 0;
  return `${record['Ngày']}_${record['Cửa hàng']}_${record['PTTT']}_${record['Mã hàng']}_${giaBan}`;
}
