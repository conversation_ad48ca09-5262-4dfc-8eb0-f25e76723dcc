import * as XLSX from 'xlsx';
import type { HTLookupRecord } from '../types';

/**
 * Loads HT lookup data for store and payment method mapping from dedicated HT file
 * Processes the entire HT file without searching for specific sheet names
 */
export async function loadHTLookupData(htFile: File): Promise<Map<string, HTLookupRecord>> {
  try {
    // Read the HT file
    const arrayBuffer = await htFile.arrayBuffer();
    const workbook = XLSX.read(arrayBuffer, { type: 'array' });

    // Use the first sheet in the HT file
    const firstSheetName = workbook.SheetNames[0];

    if (!firstSheetName) {
      console.warn('No sheets found in HT file. HT lookup will be empty.');
      return new Map();
    }

    const worksheet = workbook.Sheets[firstSheetName];

    // Try to read as JSON first (standard format)
    let htData = XLSX.utils.sheet_to_json(worksheet, { range: 1 }) as any[];
    const lookupMap = new Map<string, HTLookupRecord>();

    // Check if data has expected columns
    if (htData.length > 0 && htData[0]['Cửa hàng']) {
      // Standard format with column headers
      htData.forEach(row => {
        if (row['Cửa hàng'] && row['PTTT']) {
          const key = `${row['Cửa hàng']}_${row['PTTT']}`;

          if (!lookupMap.has(key)) {
            lookupMap.set(key, {
              store: row['Cửa hàng'],
              paymentMethod: row['PTTT'],
              storeCode: row['mã kho'] || 'sk-006',
              departmentCode: row['mã bộ phận'] || 'sk006',
              accountCode: Number(row['tk nợ']) || 1314
            });
          }
        }
      });
    } else {
      const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];

      for (let i = 1; i < rawData.length; i++) {
        const row = rawData[i];
        if (row && row[0] && row[1]) {
          const store = row[0];
          const paymentMethod = row[1];
          const key = `${store}_${paymentMethod}`;

          if (!lookupMap.has(key)) {
            lookupMap.set(key, {
              store: store,
              paymentMethod: paymentMethod,
              storeCode: row[3] || 'sk-006',      // Column 3: mã kho
              departmentCode: row[4] || 'sk006',   // Column 4: mã bộ phận
              accountCode: Number(row[6]) || 1314  // Column 6: tk nợ
            });
          }
        }
      }
    }

    return lookupMap;
  } catch (error) {
    console.error('Error loading HT lookup data:', error);
    return new Map();
  }
}
