import type {
  ExtractedInvoiceData,
  ValidationResult,
  InvoiceValidationResult,
  ValidationRule,
  InvoiceLineItem,
  TaxRateConfig,
  TaxRateRule,
  TaxRateCondition,
  TaxRateValidationResult,
} from "@/types/validation";

/**
 * Parses tax rate from string or number, handling percentage symbols and "KT"
 */
function parseTaxRate(value: unknown): number | string | undefined {
  if (typeof value === "number") {
    return value;
  }

  if (typeof value === "string") {
    // Handle "KT" case
    if (value.trim().toUpperCase() === "KT") {
      return "KT";
    }

    // Remove percentage symbol and whitespace, then parse
    const cleanValue = value.replace(/[%\s]/g, "");
    const parsed = Number(cleanValue);
    return isNaN(parsed) ? undefined : parsed;
  }

  return undefined;
}

/**
 * Interface for rule evaluation result
 */
interface RuleEvaluationResult {
  isApplicable: boolean;
  allConditionsMet: boolean;
  failedConditions: string[];
}

/**
 * Gets field value from extracted data based on field path
 * Supports both predefined mappings and dynamic JSON path resolution
 */
function getFieldValue(
  field: string,
  lineItem: InvoiceLineItem,
  extractedData: ExtractedInvoiceData,
  originalJsonData?: Record<string, unknown>
): string | undefined {
  // First try predefined mappings for backward compatibility
  switch (field) {
    case "NBan.Ten":
      return extractedData.sellerName;
    case "NBan.MST":
      return extractedData.sellerTaxCode;
    case "NBan.DChi":
      return extractedData.sellerAddress;
    case "NMua.Ten":
      return extractedData.buyerName;
    case "NMua.MST":
      return extractedData.buyerTaxCode;
    case "NMua.DChi":
      return extractedData.buyerAddress;
    case "THHDVu":
      return lineItem.itemName;
    case "DVTinh":
      return lineItem.unit;
    case "TTChung.THDon":
      return extractedData.invoiceType;
    case "TTChung.KHHDon":
      return extractedData.invoiceSerial;
    case "TTChung.HTTToan":
      return extractedData.paymentMethod;
    case "TTChung.DVTTe":
      return extractedData.currency;
    case "TTChung.PBan":
      return extractedData.version;
    case "TTChung.KHMSHDon":
      return extractedData.invoiceTemplate?.toString();
    case "TTChung.SHDon":
      return extractedData.invoiceNumber;
    case "TTChung.NLap":
      return extractedData.invoiceDate;
    case "TTChung.TGia":
      return extractedData.exchangeRate?.toString();
    case "TTChung.MSTTCGP":
      return extractedData.taxAuthorityCode;
    case "HHDVu.TSuat":
      return lineItem.taxRate?.toString();
    case "HHDVu.TChat":
      return lineItem.characteristics?.toString();
    case "HHDVu.STT":
      return lineItem.stt?.toString();
    case "HHDVu.MHHDVu":
      return lineItem.itemCode;
    case "HHDVu.SLuong":
      return lineItem.quantity?.toString();
    case "HHDVu.DGia":
      return lineItem.unitPrice?.toString();
    case "HHDVu.TLCKhau":
      return lineItem.discountRate?.toString();
    case "HHDVu.STCKhau":
      return lineItem.discountAmount?.toString();
    case "HHDVu.ThTien":
      return lineItem.amount?.toString();
    default:
      // Try dynamic JSON path resolution for custom fields
      if (originalJsonData) {
        return getValueFromJsonPath(field, originalJsonData, lineItem);
      }
      return undefined;
  }
}

/**
 * Extracts value from JSON data using dot notation path
 * Supports both invoice-level and line-item-level fields
 */
function getValueFromJsonPath(
  path: string,
  jsonData: Record<string, unknown>,
  lineItem?: InvoiceLineItem
): string | undefined {
  try {
    // Navigate to the invoice structure
    const hdon = jsonData.HDon as Record<string, unknown>;
    if (!hdon) return undefined;

    const dlhdon = hdon.DLHDon as Record<string, unknown>;
    if (!dlhdon) return undefined;

    // Handle different path types
    if (path.startsWith("TTChung.")) {
      // Invoice general information
      const ttchung = dlhdon.TTChung as Record<string, unknown>;
      if (!ttchung) return undefined;

      const fieldName = path.replace("TTChung.", "");
      if (fieldName.includes(".")) {
        // Nested path like TTChung.TTHDLQuan.TCHDon
        return getNestedValue(ttchung, fieldName);
      } else {
        return ttchung[fieldName]?.toString();
      }
    } else if (path.startsWith("NBan.")) {
      // Seller information
      const ndhdon = dlhdon.NDHDon as Record<string, unknown>;
      if (!ndhdon) return undefined;

      const nban = ndhdon.NBan as Record<string, unknown>;
      if (!nban) return undefined;

      const fieldName = path.replace("NBan.", "");
      return nban[fieldName]?.toString();
    } else if (path.startsWith("NMua.")) {
      // Buyer information
      const ndhdon = dlhdon.NDHDon as Record<string, unknown>;
      if (!ndhdon) return undefined;

      const nmua = ndhdon.NMua as Record<string, unknown>;
      if (!nmua) return undefined;

      const fieldName = path.replace("NMua.", "");
      return nmua[fieldName]?.toString();
    } else if (path.startsWith("HHDVu.")) {
      // Line item information - use the provided lineItem data
      const fieldName = path.replace("HHDVu.", "");
      return getLineItemField(lineItem, fieldName);
    } else if (path.startsWith("TToan.")) {
      // Total amounts
      const ndhdon = dlhdon.NDHDon as Record<string, unknown>;
      if (!ndhdon) return undefined;

      const ttoan = ndhdon.TToan as Record<string, unknown>;
      if (!ttoan) return undefined;

      const fieldName = path.replace("TToan.", "");
      if (fieldName.includes(".")) {
        // Nested path like TToan.THTTLTSuat.LTSuat.TSuat
        return getNestedValue(ttoan, fieldName);
      } else {
        return ttoan[fieldName]?.toString();
      }
    }

    return undefined;
  } catch (error) {
    console.error(`Error extracting value from path ${path}:`, error);
    return undefined;
  }
}

/**
 * Gets nested value from object using dot notation
 */
function getNestedValue(
  obj: Record<string, unknown>,
  path: string
): string | undefined {
  const parts = path.split(".");
  let current: unknown = obj;

  for (const part of parts) {
    if (current && typeof current === "object" && part in current) {
      current = (current as Record<string, unknown>)[part];
    } else {
      return undefined;
    }
  }

  return current?.toString();
}

/**
 * Gets field value from line item data
 */
function getLineItemField(
  lineItem: InvoiceLineItem | undefined,
  fieldName: string
): string | undefined {
  if (!lineItem) return undefined;

  switch (fieldName) {
    case "TSuat":
      return lineItem.taxRate?.toString();
    case "TChat":
      return lineItem.characteristics?.toString();
    case "STT":
      return lineItem.stt?.toString();
    case "MHHDVu":
      return lineItem.itemCode;
    case "THHDVu":
      return lineItem.itemName;
    case "DVTinh":
      return lineItem.unit;
    case "SLuong":
      return lineItem.quantity?.toString();
    case "DGia":
      return lineItem.unitPrice?.toString();
    case "TLCKhau":
      return lineItem.discountRate?.toString();
    case "STCKhau":
      return lineItem.discountAmount?.toString();
    case "ThTien":
      return lineItem.amount?.toString();
    case "TThue":
      return lineItem.taxAmount?.toString();
    default:
      return undefined;
  }
}

/**
 * Checks if a pattern with wildcards matches a value
 */
function matchesWildcardPattern(value: string, pattern: string): boolean {
  // Convert pattern to lowercase for case-insensitive matching
  const lowerValue = value.toLowerCase();
  const lowerPattern = pattern.toLowerCase();

  // If no wildcards, do exact match
  if (!lowerPattern.includes("*")) {
    return lowerValue === lowerPattern;
  }

  // Handle different wildcard patterns
  if (lowerPattern === "*") {
    // Single * matches everything
    return true;
  } else if (lowerPattern.startsWith("*") && lowerPattern.endsWith("*")) {
    // *text* - contains
    const searchText = lowerPattern.slice(1, -1);
    return lowerValue.includes(searchText);
  } else if (lowerPattern.startsWith("*")) {
    // *text - ends with
    const searchText = lowerPattern.slice(1);
    return lowerValue.endsWith(searchText);
  } else if (lowerPattern.endsWith("*")) {
    // text* - starts with
    const searchText = lowerPattern.slice(0, -1);
    return lowerValue.startsWith(searchText);
  } else {
    // Multiple * in middle - convert to regex
    const regexPattern = lowerPattern
      .replace(/[.*+?^${}()|[\]\\]/g, "\\$&") // Escape special regex chars except *
      .replace(/\\\*/g, ".*"); // Convert * to .*

    const regex = new RegExp(`^${regexPattern}$`);
    return regex.test(lowerValue);
  }
}

/**
 * Evaluates a single condition against the data
 */
function evaluateCondition(
  condition: TaxRateCondition,
  lineItem: InvoiceLineItem,
  extractedData: ExtractedInvoiceData,
  originalJsonData?: Record<string, unknown>
): { isValid: boolean; message?: string } {
  const fieldValue = getFieldValue(
    condition.field,
    lineItem,
    extractedData,
    originalJsonData
  );

  if (!fieldValue) {
    return {
      isValid: false,
      message: `Không tìm thấy giá trị cho trường ${condition.field}`,
    };
  }

  const value = fieldValue.toLowerCase();
  const conditionValue = condition.value.toLowerCase();

  let isValid = false;

  // Check if condition value contains wildcards
  if (condition.value.includes("*")) {
    // Use wildcard pattern matching
    isValid = matchesWildcardPattern(fieldValue, condition.value);
  } else {
    // Use traditional operator-based matching
    switch (condition.operator) {
      case "equals":
        isValid = value === conditionValue;
        break;
      case "contains":
        isValid = value.includes(conditionValue);
        break;
      case "startsWith":
        isValid = value.startsWith(conditionValue);
        break;
      case "endsWith":
        isValid = value.endsWith(conditionValue);
        break;
    }
  }

  if (!isValid) {
    let operatorText: string;

    if (condition.value.includes("*")) {
      // Describe wildcard pattern
      if (condition.value === "*") {
        operatorText = "khớp với bất kỳ giá trị nào";
      } else if (
        condition.value.startsWith("*") &&
        condition.value.endsWith("*")
      ) {
        operatorText = `chứa "${condition.value.slice(1, -1)}"`;
      } else if (condition.value.startsWith("*")) {
        operatorText = `kết thúc bằng "${condition.value.slice(1)}"`;
      } else if (condition.value.endsWith("*")) {
        operatorText = `bắt đầu bằng "${condition.value.slice(0, -1)}"`;
      } else {
        operatorText = `khớp với pattern "${condition.value}"`;
      }
    } else {
      // Traditional operator descriptions
      operatorText =
        {
          equals: "bằng",
          contains: "chứa",
          startsWith: "bắt đầu bằng",
          endsWith: "kết thúc bằng",
        }[condition.operator] || "khớp với";
      operatorText += ` "${condition.value}"`;
    }

    return {
      isValid: false,
      message: `Trường ${condition.field} (${fieldValue}) không ${operatorText}`,
    };
  }

  return { isValid: true };
}

/**
 * Evaluates all conditions of a tax rate rule against line item and invoice data
 * Logic: Supports both AND and OR logic based on rule.logicOperator
 */
function evaluateRuleConditions(
  rule: TaxRateRule,
  lineItem: InvoiceLineItem,
  extractedData: ExtractedInvoiceData,
  originalJsonData?: Record<string, unknown>
): RuleEvaluationResult {
  const failedConditions: string[] = [];

  // If no conditions are defined, the rule doesn't apply to this line item
  if (rule.conditions.length === 0) {
    return {
      isApplicable: false,
      allConditionsMet: false,
      failedConditions: ["Quy tắc không có điều kiện nào được định nghĩa"],
    };
  }

  // Evaluate all conditions
  const conditionResults: {
    condition: TaxRateCondition;
    matches: boolean;
    message?: string;
  }[] = [];

  for (const condition of rule.conditions) {
    const conditionResult = evaluateCondition(
      condition,
      lineItem,
      extractedData,
      originalJsonData
    );

    conditionResults.push({
      condition,
      matches: conditionResult.isValid,
      message: conditionResult.message,
    });

    if (!conditionResult.isValid && conditionResult.message) {
      failedConditions.push(conditionResult.message);
    }
  }

  // Apply logic operator (AND or OR)
  let conditionsMatch: boolean;
  if (rule.logicOperator === "OR") {
    // OR logic: at least one condition must match
    conditionsMatch = conditionResults.some((result) => result.matches);
  } else {
    // AND logic (default): all conditions must match
    conditionsMatch = conditionResults.every((result) => result.matches);
  }

  // If conditions match according to logic operator, check tax rate
  if (conditionsMatch) {
    // For OR logic, find the matching condition(s) and use their tax rate(s)
    // For AND logic, all conditions must have the same tax rate
    if (rule.logicOperator === "OR") {
      // OR logic: find the first matching condition and use its tax rate
      const matchingCondition = conditionResults.find(
        (result) => result.matches
      );
      if (matchingCondition) {
        const expectedTaxRate = matchingCondition.condition.taxRate;
        const taxRateValid = expectedTaxRate === lineItem.taxRate;
        if (!taxRateValid) {
          failedConditions.push(
            `Thuế suất ${lineItem.taxRate} không khớp với thuế suất yêu cầu ${expectedTaxRate} cho quy tắc "${rule.name}"`
          );
          conditionsMatch = false;
        }
      }
    } else {
      // AND logic: all conditions must have the same tax rate
      const expectedTaxRate = rule.conditions[0].taxRate;

      // Verify all conditions have the same tax rate (consistency check)
      const inconsistentTaxRates = rule.conditions.some(
        (c) => c.taxRate !== expectedTaxRate
      );
      if (inconsistentTaxRates) {
        failedConditions.push(
          `Quy tắc "${rule.name}" có các điều kiện với thuế suất khác nhau - cần thống nhất`
        );
        conditionsMatch = false;
      } else {
        // Check if actual tax rate matches expected tax rate
        const taxRateValid = expectedTaxRate === lineItem.taxRate;
        if (!taxRateValid) {
          failedConditions.push(
            `Thuế suất ${lineItem.taxRate} không khớp với thuế suất yêu cầu ${expectedTaxRate} cho quy tắc "${rule.name}"`
          );
          conditionsMatch = false;
        }
      }
    }
  }

  return {
    isApplicable: rule.conditions.length > 0, // Rule is applicable if it has conditions
    allConditionsMet: conditionsMatch,
    failedConditions,
  };
}

/**
 * Generates detailed error message for tax rate validation failure
 */
function generateDetailedErrorMessage(
  lineItem: InvoiceLineItem,
  failedConditions: string[]
): string {
  const taxRate = lineItem.taxRate;

  let message = `Thuế suất ${taxRate} không hợp lệ.`;

  // Add specific failed conditions if available
  if (failedConditions.length > 0) {
    message += `\n\nChi tiết lỗi:\n${failedConditions.join("\n")}`;
  }

  return message;
}

/**
 * Extracts invoice data from parsed XML/JSON structure
 */
export function extractInvoiceData(
  jsonData: Record<string, unknown>
): ExtractedInvoiceData {
  const extractedData: ExtractedInvoiceData = {};

  try {
    // Navigate through the JSON structure based on the sample provided
    const hdon = jsonData.HDon as Record<string, unknown>;
    if (!hdon) return extractedData;

    const dlhdon = hdon.DLHDon as Record<string, unknown>;
    if (!dlhdon) return extractedData;

    // Extract general invoice information (TTChung)
    const ttchung = dlhdon.TTChung as Record<string, unknown>;
    if (ttchung) {
      extractedData.version = String(ttchung.PBan || "");
      extractedData.invoiceNumber = String(ttchung.SHDon || "");
      extractedData.invoiceDate = String(ttchung.NLap || "");
      extractedData.invoiceType = String(ttchung.THDon || "");
      extractedData.invoiceSerial = String(ttchung.KHHDon || "");
      extractedData.invoiceTemplate = ttchung.KHMSHDon
        ? Number(ttchung.KHMSHDon)
        : undefined;
      extractedData.currency = String(ttchung.DVTTe || "");
      extractedData.exchangeRate = ttchung.TGia
        ? Number(ttchung.TGia)
        : undefined;
      extractedData.paymentMethod = String(ttchung.HTTToan || "");
      extractedData.taxAuthorityCode = String(ttchung.MSTTCGP || "");
    }

    const ndhdon = dlhdon.NDHDon as Record<string, unknown>;
    if (!ndhdon) return extractedData;

    // Extract seller information (NBan)
    const nban = ndhdon.NBan as Record<string, unknown>;
    if (nban) {
      extractedData.sellerTaxCode = String(nban.MST || "");
      extractedData.sellerName = String(nban.Ten || "");
      extractedData.sellerAddress = String(nban.DChi || "");
      extractedData.sellerPhone = String(nban.SDThoai || "");
      extractedData.sellerBankAccount = String(nban.STKNHang || "");
      extractedData.sellerBankName = String(nban.TNHang || "");
    }

    // Extract buyer information (NMua)
    const nmua = ndhdon.NMua as Record<string, unknown>;
    if (nmua) {
      extractedData.buyerTaxCode = String(nmua.MST || "");
      extractedData.buyerName = String(nmua.Ten || "");
      extractedData.buyerAddress = String(nmua.DChi || "");
    }

    // Extract line items with tax rates (DSHHDVu.HHDVu)
    const dshhDvu = ndhdon.DSHHDVu as Record<string, unknown>;
    if (dshhDvu) {
      const hhDvuArray = dshhDvu.HHDVu;
      if (Array.isArray(hhDvuArray)) {
        extractedData.lineItems = hhDvuArray.map(
          (item: Record<string, unknown>) => ({
            stt: item.STT ? Number(item.STT) : undefined,
            itemName: String(item.THHDVu || ""),
            itemCode: String(item.MHHDVu || ""),
            unit: String(item.DVTinh || ""),
            quantity: item.SLuong ? Number(item.SLuong) : undefined,
            unitPrice: item.DGia ? Number(item.DGia) : undefined,
            amount: item.ThTien ? Number(item.ThTien) : undefined,
            taxRate: item.TSuat ? parseTaxRate(item.TSuat) : undefined,
            taxAmount: item.TThue ? Number(item.TThue) : undefined,
            characteristics: item.TChat ? Number(item.TChat) : undefined,
            discountRate: item.TLCKhau ? Number(item.TLCKhau) : undefined,
            discountAmount: item.STCKhau ? Number(item.STCKhau) : undefined,
          })
        );
      } else if (hhDvuArray && typeof hhDvuArray === "object") {
        // Handle single item case (not an array)
        const item = hhDvuArray as Record<string, unknown>;
        extractedData.lineItems = [
          {
            stt: item.STT ? Number(item.STT) : undefined,
            itemName: String(item.THHDVu || ""),
            itemCode: String(item.MHHDVu || ""),
            unit: String(item.DVTinh || ""),
            quantity: item.SLuong ? Number(item.SLuong) : undefined,
            unitPrice: item.DGia ? Number(item.DGia) : undefined,
            amount: item.ThTien ? Number(item.ThTien) : undefined,
            taxRate: item.TSuat ? parseTaxRate(item.TSuat) : undefined,
            taxAmount: item.TThue ? Number(item.TThue) : undefined,
            characteristics: item.TChat ? Number(item.TChat) : undefined,
            discountRate: item.TLCKhau ? Number(item.TLCKhau) : undefined,
            discountAmount: item.STCKhau ? Number(item.STCKhau) : undefined,
          },
        ];
      }
    }

    // Extract total amounts (TToan)
    const ttoan = ndhdon.TToan as Record<string, unknown>;
    if (ttoan) {
      extractedData.totalAmountBeforeTax = ttoan.TTCKTMai
        ? Number(ttoan.TTCKTMai)
        : undefined;
      extractedData.totalAmount = ttoan.TgTTTBSo
        ? Number(ttoan.TgTTTBSo)
        : undefined;
      extractedData.totalAmountInWords = String(ttoan.TgTTTBChu || "");
    }
  } catch (error) {
    console.error("Error extracting invoice data:", error);
  }

  return extractedData;
}

/**
 * Validates extracted data against validation rules
 * Only validates buyer information (NMua)
 */
export function validateInvoiceData(
  extractedData: ExtractedInvoiceData,
  validationRules: ValidationRule[]
): ValidationResult[] {
  const results: ValidationResult[] = [];

  // Validate buyer information only
  if (extractedData.buyerTaxCode) {
    const rule = validationRules.find(
      (r) => r.taxCode === extractedData.buyerTaxCode
    );

    // Validate buyer tax code
    results.push({
      field: "Mã số thuế người mua",
      extractedValue: extractedData.buyerTaxCode,
      expectedValue: rule?.taxCode,
      isValid: !!rule,
      message: rule
        ? "Mã số thuế hợp lệ"
        : "Mã số thuế không tìm thấy trong danh sách",
    });

    // Validate buyer name
    if (rule && extractedData.buyerName) {
      const nameMatch =
        normalizeString(extractedData.buyerName) === normalizeString(rule.name);
      results.push({
        field: "Tên người mua",
        extractedValue: extractedData.buyerName,
        expectedValue: rule.name,
        isValid: nameMatch,
        message: nameMatch
          ? "Tên khớp với dữ liệu tham chiếu"
          : "Tên không khớp với dữ liệu tham chiếu",
      });
    }

    // Validate buyer address
    if (rule && extractedData.buyerAddress) {
      const addressMatch = compareVietnameseAddresses(
        extractedData.buyerAddress,
        rule.address
      );
      results.push({
        field: "Địa chỉ người mua",
        extractedValue: extractedData.buyerAddress,
        expectedValue: rule.address,
        isValid: addressMatch,
        message: addressMatch
          ? "Địa chỉ khớp với dữ liệu tham chiếu (có hỗ trợ viết tắt)"
          : "Địa chỉ không khớp với dữ liệu tham chiếu",
      });
    }
  }

  return results;
}

/**
 * Validates tax rates in invoice line items against configured rules
 * Performs comprehensive validation of all rule conditions
 */
export function validateTaxRates(
  extractedData: ExtractedInvoiceData,
  taxRateConfig: TaxRateConfig,
  originalJsonData?: Record<string, unknown>
): TaxRateValidationResult[] {
  const results: TaxRateValidationResult[] = [];

  // Skip validation if tax rate validation is disabled or no line items
  if (!extractedData.lineItems) {
    return results;
  }

  extractedData.lineItems.forEach((lineItem, index) => {
    if (lineItem.taxRate !== undefined) {
      // Evaluate each rule comprehensively
      let isValid = false;
      let appliedRule: string | undefined;
      let message = "";
      const failedConditions: string[] = [];
      let hasApplicableRules = false;

      for (const rule of taxRateConfig.rules) {
        const ruleEvaluation = evaluateRuleConditions(
          rule,
          lineItem,
          extractedData,
          originalJsonData
        );

        if (ruleEvaluation.isApplicable) {
          hasApplicableRules = true;

          if (ruleEvaluation.allConditionsMet) {
            // Rule applies and all conditions are met
            isValid = true;
            appliedRule = rule.name;
            message = `Thuế suất hợp lệ theo quy tắc: ${rule.name}`;

            // Add context information about conditions
            if (rule.conditions.length > 0) {
              const conditionDescriptions = rule.conditions.map((condition) => {
                const fieldLabel =
                  {
                    "NBan.Ten": "tên người bán",
                    "NBan.MST": "MST người bán",
                    "NBan.DChi": "địa chỉ người bán",
                    "NMua.Ten": "tên người mua",
                    "NMua.MST": "MST người mua",
                    "NMua.DChi": "địa chỉ người mua",
                    THHDVu: "tên hàng hóa",
                    DVTinh: "đơn vị tính",
                    "TTChung.THDon": "loại hóa đơn",
                    "TTChung.KHHDon": "ký hiệu hóa đơn",
                    "TTChung.HTTToan": "hình thức thanh toán",
                    "TTChung.DVTTe": "đơn vị tiền tệ",
                  }[condition.field] || condition.field;

                const operatorLabel = {
                  equals: "bằng",
                  contains: "chứa",
                  startsWith: "bắt đầu bằng",
                  endsWith: "kết thúc bằng",
                }[condition.operator];

                return `${fieldLabel} ${operatorLabel} "${condition.value}"`;
              });
              message += `\n(${conditionDescriptions.join(", ")})`;
            }
            break;
          } else {
            // Rule could apply but some conditions failed
            failedConditions.push(
              ...ruleEvaluation.failedConditions.map(
                (condition) => `${rule.name}: ${condition}`
              )
            );
          }
        }
      }

      if (!isValid) {
        if (!hasApplicableRules) {
          // No rules match this line item at all - consider it valid by default
          isValid = true;
          message = ""; // Leave blank for valid-by-default items
        } else {
          // Some rules were applicable but failed validation
          message = generateDetailedErrorMessage(lineItem, failedConditions);
        }
      }

      results.push({
        lineItemIndex: index,
        itemName: lineItem.itemName,
        extractedTaxRate: lineItem.taxRate,
        isValid,
        message,
        appliedRule,
      });
    }
  });

  return results;
}

/**
 * Performs complete validation of an invoice
 */
export function validateInvoice(
  fileName: string,
  jsonData: Record<string, unknown>,
  validationRules: ValidationRule[],
  taxRateConfig?: TaxRateConfig
): InvoiceValidationResult {
  const errors: string[] = [];

  try {
    // Check if jsonData has error
    if ("error" in jsonData && typeof jsonData.error === "string") {
      errors.push(`Lỗi phân tích XML: ${jsonData.error}`);
      return {
        fileName,
        extractedData: {},
        validationResults: [],
        overallValid: false,
        errors,
      };
    }

    // Extract data
    const extractedData = extractInvoiceData(jsonData);

    // Validate data
    const validationResults = validateInvoiceData(
      extractedData,
      validationRules
    );

    // Validate tax rates if configuration is provided
    const taxRateValidationResults = taxRateConfig
      ? validateTaxRates(extractedData, taxRateConfig, jsonData)
      : undefined;

    // Check overall validity (including tax rate validation if enabled)
    const buyerValidationValid =
      validationResults.length > 0 && validationResults.every((r) => r.isValid);
    const taxRateValidationValid =
      !taxRateValidationResults ||
      taxRateValidationResults.length === 0 ||
      taxRateValidationResults.every((r) => r.isValid);

    const overallValid = buyerValidationValid && taxRateValidationValid;

    return {
      fileName,
      extractedData,
      validationResults,
      taxRateValidationResults,
      overallValid,
      errors,
    };
  } catch (error) {
    errors.push(
      `Lỗi xử lý: ${error instanceof Error ? error.message : String(error)}`
    );
    return {
      fileName,
      extractedData: {},
      validationResults: [],
      overallValid: false,
      errors,
    };
  }
}

/**
 * Address abbreviation mappings for Vietnamese addresses and business entities
 */
const ADDRESS_ABBREVIATIONS: Record<string, string[]> = {
  phường: ["phường", "phuong"],
  "thị trấn": ["thị trấn", "thi tran"],
  quận: ["quận", "quan"],
  "thị xã": ["thị xã", "thi xa"],
  "thành phố": ["thành phố", "thanh pho"],
  "việt nam": ["việt nam", "viet nam", "vietnam"],
  // Business entity abbreviations
  "trách nhiệm hữu hạn": ["trách nhiệm hữu hạn"],
  "doanh nghiệp tư nhân": ["doanh nghiệp tư nhân"],
  "hợp danh": ["hợp danh"],
  "khu công nghiệp": ["khu công nghiệp"],
  "khu chế xuất": ["khu chế xuất"],
  "khu công nghệ cao": ["khu công nghệ cao"],
  "sản xuất": ["sản xuất"],
  "chi nhánh": ["chi nhánh"],
  "văn phòng đại diện": ["văn phòng đại diện"],
};

/**
 * Single character abbreviations that need special handling
 */
const SINGLE_CHAR_ABBREVIATIONS: Record<string, string> = {
  p: "phường",
  q: "quận",
  tt: "thị trấn",
  tx: "thị xã",
  tp: "thành phố",
  vn: "việt nam",
  tnhh: "trách nhiệm hữu hạn",
  dntn: "doanh nghiệp tư nhân",
  hd: "hợp danh",
  kcn: "khu công nghiệp",
  kcx: "khu chế xuất",
  kcnc: "khu công nghệ cao",
  sx: "sản xuất",
  cn: "chi nhánh",
  vpđd: "văn phòng đại diện",
};

/**
 * Normalizes Vietnamese address and business entities by expanding abbreviations and standardizing format
 */
function normalizeVietnameseAddress(address: string): string {
  let normalized = address.trim().toLowerCase();

  // First, normalize spacing around commas (add space after comma if missing)
  normalized = normalized.replace(/,(\S)/g, ", $1");

  // Remove extra spaces before commas
  normalized = normalized.replace(/\s+,/g, ",");

  // First, handle single character abbreviations with strict word boundaries
  Object.entries(SINGLE_CHAR_ABBREVIATIONS).forEach(([abbrev, fullForm]) => {
    const regex = new RegExp(
      `\\b${abbrev.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`,
      "gi"
    );
    normalized = normalized.replace(regex, fullForm);
  });

  // Then handle multi-word abbreviations
  Object.entries(ADDRESS_ABBREVIATIONS).forEach(([fullForm, abbreviations]) => {
    abbreviations.forEach((abbrev) => {
      // For multi-word phrases, use word boundaries
      const regex = new RegExp(
        `\\b${abbrev.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`,
        "gi"
      );
      normalized = normalized.replace(regex, fullForm);
    });
  });

  // Final cleanup: remove extra spaces and normalize
  normalized = normalized.replace(/\s+/g, " ").trim();

  return normalized;
}

/**
 * Compares two Vietnamese addresses with abbreviation support for addresses and business entities
 */
export function compareVietnameseAddresses(
  address1: string,
  address2: string
): boolean {
  const normalized1 = normalizeVietnameseAddress(address1);
  const normalized2 = normalizeVietnameseAddress(address2);

  // Direct comparison after normalization
  if (normalized1 === normalized2) {
    return true;
  }

  // Handle optional "Việt Nam" suffix - remove it for comparison if present
  const withoutVietnam1 = normalized1.replace(/,?\s*việt nam\s*$/i, "").trim();
  const withoutVietnam2 = normalized2.replace(/,?\s*việt nam\s*$/i, "").trim();

  // Compare without "Việt Nam" suffix
  if (withoutVietnam1 === withoutVietnam2) {
    return true;
  }

  // Also try normalizing both addresses with different strategies
  // Strategy 1: Normalize both to full forms
  const fullForm1 = normalizeVietnameseAddress(address1);
  const fullForm2 = normalizeVietnameseAddress(address2);

  // Strategy 2: Create abbreviated versions for comparison
  const abbrev1 = createAbbreviatedAddress(address1);
  const abbrev2 = createAbbreviatedAddress(address2);

  // Check if any combination matches (with and without "Việt Nam")
  const fullForm1NoVN = fullForm1.replace(/,?\s*việt nam\s*$/i, "").trim();
  const fullForm2NoVN = fullForm2.replace(/,?\s*việt nam\s*$/i, "").trim();
  const abbrev1NoVN = abbrev1.replace(/,?\s*việt nam\s*$/i, "").trim();
  const abbrev2NoVN = abbrev2.replace(/,?\s*việt nam\s*$/i, "").trim();

  if (
    fullForm1 === fullForm2 ||
    abbrev1 === abbrev2 ||
    fullForm1 === abbrev2 ||
    abbrev1 === fullForm2 ||
    fullForm1NoVN === fullForm2NoVN ||
    abbrev1NoVN === abbrev2NoVN ||
    fullForm1NoVN === abbrev2NoVN ||
    abbrev1NoVN === fullForm2NoVN
  ) {
    return true;
  }

  // Fuzzy comparison - check if one address contains the key parts of the other
  const parts1 = withoutVietnam1
    .split(",")
    .map((part) => part.trim())
    .filter((part) => part.length > 0);
  const parts2 = withoutVietnam2
    .split(",")
    .map((part) => part.trim())
    .filter((part) => part.length > 0);

  // Check if most significant parts match (at least 70% of parts should match)
  let matchCount = 0;
  const minParts = Math.min(parts1.length, parts2.length);

  for (const part1 of parts1) {
    for (const part2 of parts2) {
      if (
        part1.includes(part2) ||
        part2.includes(part1) ||
        calculateSimilarity(part1, part2) > 0.8 ||
        compareAddressParts(part1, part2)
      ) {
        matchCount++;
        break;
      }
    }
  }

  return matchCount >= Math.max(1, Math.floor(minParts * 0.7));
}

/**
 * Creates abbreviated version of Vietnamese address and business entities
 */
function createAbbreviatedAddress(address: string): string {
  let abbreviated = address.trim().toLowerCase();

  // First, normalize spacing around commas (add space after comma if missing)
  abbreviated = abbreviated.replace(/,(\S)/g, ", $1");

  // Remove extra spaces before commas
  abbreviated = abbreviated.replace(/\s+,/g, ",");

  // Create reverse mapping from SINGLE_CHAR_ABBREVIATIONS
  const reverseAbbreviations: Record<string, string> = {};
  Object.entries(SINGLE_CHAR_ABBREVIATIONS).forEach(([abbrev, fullForm]) => {
    reverseAbbreviations[fullForm] = abbrev;
  });

  // Replace full forms with abbreviations
  Object.entries(reverseAbbreviations).forEach(([fullForm, abbrev]) => {
    const regex = new RegExp(
      `\\b${fullForm.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}\\b`,
      "gi"
    );
    abbreviated = abbreviated.replace(regex, abbrev);
  });

  // Final cleanup: remove extra spaces and normalize
  abbreviated = abbreviated.replace(/\s+/g, " ").trim();

  return abbreviated;
}

/**
 * Compares two address parts with abbreviation awareness
 */
function compareAddressParts(part1: string, part2: string): boolean {
  const normalized1 = normalizeVietnameseAddress(part1);
  const normalized2 = normalizeVietnameseAddress(part2);
  const abbrev1 = createAbbreviatedAddress(part1);
  const abbrev2 = createAbbreviatedAddress(part2);

  return (
    normalized1 === normalized2 ||
    abbrev1 === abbrev2 ||
    normalized1 === abbrev2 ||
    abbrev1 === normalized2 ||
    // Check if one contains the other after normalization
    normalized1.includes(normalized2) ||
    normalized2.includes(normalized1) ||
    abbrev1.includes(abbrev2) ||
    abbrev2.includes(abbrev1)
  );
}

/**
 * Calculate string similarity using simple character comparison
 */
function calculateSimilarity(str1: string, str2: string): number {
  const longer = str1.length > str2.length ? str1 : str2;
  const shorter = str1.length > str2.length ? str2 : str1;

  if (longer.length === 0) return 1.0;

  const editDistance = getEditDistance(longer, shorter);
  return (longer.length - editDistance) / longer.length;
}

/**
 * Calculate edit distance between two strings
 */
function getEditDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1)
    .fill(null)
    .map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
}

/**
 * Normalizes strings for comparison (removes extra spaces, converts to lowercase)
 */
function normalizeString(str: string): string {
  return str.trim().toLowerCase().replace(/\s+/g, " ");
}
