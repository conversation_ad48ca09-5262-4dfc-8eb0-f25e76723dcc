import type { ExcelParseResult } from '@/types/excel';

export const sortExcelData = (result: ExcelParseResult): ExcelParseResult => {
  const unitColumn = result.headers.find(header => {
    const name = header.name.toLowerCase();
    return header.column === 'D' ||
           name.includes('đơn vị tính') ||
           name.includes('dvt') ||
           name === 'dvt' ||
           name.includes('unit');
  });

  const materialNameColumn = result.headers.find(header => {
    const name = header.name.toLowerCase();
    return header.column === 'C' ||
           name.includes('tên vật tư') ||
           name.includes('tên') ||
           name.includes('material') ||
           name.includes('vật tư');
  });

  if (!unitColumn || !materialNameColumn) {
    console.warn('Could not find required columns for sorting:', {
      unitColumn: unitColumn?.name || 'Not found',
      materialNameColumn: materialNameColumn?.name || 'Not found',
      availableColumns: result.headers.map(h => `${h.column}: ${h.name}`)
    });
    return result;
  }

  console.log('Sorting by:', {
    primarySort: `${unitColumn.column} (${unitColumn.name})`,
    secondarySort: `${materialNameColumn.column} (${materialNameColumn.name})`
  });

  const sortedRows = [...result.rows].sort((a, b) => {
    const unitA = String(a.values[unitColumn.name] || '').trim();
    const unitB = String(b.values[unitColumn.name] || '').trim();
    const materialA = String(a.values[materialNameColumn.name] || '').trim();
    const materialB = String(b.values[materialNameColumn.name] || '').trim();

    const firstLetterA = unitA.charAt(0).toLowerCase();
    const firstLetterB = unitB.charAt(0).toLowerCase();

    const firstLetterComparison = firstLetterA.localeCompare(firstLetterB, 'vi', {
      sensitivity: 'base',
      numeric: true,
      ignorePunctuation: true
    });

    if (firstLetterComparison !== 0) {
      return firstLetterComparison;
    }

    const fullUnitComparison = unitA.localeCompare(unitB, 'vi', {
      sensitivity: 'base',
      numeric: true,
      ignorePunctuation: true
    });

    if (fullUnitComparison !== 0) {
      return fullUnitComparison;
    }

    return materialA.localeCompare(materialB, 'vi', {
      sensitivity: 'base',
      numeric: true,
      ignorePunctuation: true
    });
  });

  return {
    ...result,
    rows: sortedRows
  };
};
