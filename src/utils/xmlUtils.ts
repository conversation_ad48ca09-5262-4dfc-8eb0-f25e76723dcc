import { XMLParser, type X2jOptions } from 'fast-xml-parser'; // Added 'type' for X2jOptions import

/**
 * Converts an XML string into a JSON object.
 * @param xmlStr   The XML content as a string.
 * @param opts     Additional options (tags to ignore, prefix, etc.).
 */
export function xmlToJson(
  xmlStr: string,
  opts?: {
    ignoreTags?: string[];           // Tags to skip completely
    attrPrefix?: string;             // Prefix for attributes
    lowerCaseTags?: boolean;         // Convert tagName to lowerCase
  }
): Record<string, unknown> | { error: string } {
  // --- 1. Set default options
  const {
    ignoreTags = ['DSCKS', 'DLQRCode', 'Signature', 'X509Certificate'], // Default tags to ignore
    attrPrefix = '@',      // e.g., "Ten@attr"
    lowerCaseTags = false, // Whether to convert tag names to lowercase
  } = opts || {};

  // --- 2. Parse XML to object
  const parserOpts: Partial<X2jOptions> = {
    ignoreAttributes: false,        // Process attributes
    attributeNamePrefix: attrPrefix, // Prefix for attribute names
    parseTagValue: true,            // Parse tag values (e.g., to boolean/number if possible)
    parseAttributeValue: true,      // Parse attribute values
    trimValues: true,               // Trim whitespace from values
  };

  const parser = new XMLParser(parserOpts);
  let parsedObj: Record<string, unknown>;
  try {
    parsedObj = parser.parse(xmlStr) as Record<string, unknown>;
  } catch (error) {
    console.error("Error parsing XML:", error);
    return { error: "Invalid XML format" };
  }

  // --- 3. Filter out ignored tags recursively
  function filterIgnoredTags(obj: unknown): unknown {
    if (obj === null || obj === undefined || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(filterIgnoredTags);
    }

    const filteredObj: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(obj as Record<string, unknown>)) {
      const tag = lowerCaseTags ? key.toLowerCase() : key;
      if (!ignoreTags.includes(tag)) {
        filteredObj[key] = filterIgnoredTags(value);
      }
    }
    return filteredObj;
  }

  return filterIgnoredTags(parsedObj) as Record<string, unknown>;
}

/**
 * Converts an XML string into a flat list of records (columnar).
 * @param xmlStr   The XML content as a string.
 * @param opts     Additional options (tags to ignore, prefix, etc.).
 */
export function xmlToRows(
  xmlStr: string,
  opts?: {
    ignoreTags?: string[];           // Tags to skip completely
    attrPrefix?: string;             // Prefix for attributes
    joiner?: string;                 // Path joining character
    lowerCaseTags?: boolean;         // Convert tagName to lowerCase
  }
): Record<string, string>[] {
  // --- 1. Set default options
  const {
    ignoreTags = ['DSCKS', 'DLQRCode', 'Signature', 'X509Certificate'], // Default tags to ignore
    attrPrefix = '@',      // e.g., "Ten@attr"
    joiner = '.',          // Path joiner, e.g., "parent.child.attr"
    lowerCaseTags = false, // Whether to convert tag names to lowercase
  } = opts || {};

  // --- 2. Parse XML to object
  const parserOpts: Partial<X2jOptions> = { // Changed X2jOptionsOptional to X2jOptions
    ignoreAttributes: false,        // Process attributes
    attributeNamePrefix: attrPrefix, // Prefix for attribute names
    parseTagValue: true,            // Parse tag values (e.g., to boolean/number if possible)
    parseAttributeValue: true,      // Parse attribute values
    trimValues: true,               // Trim whitespace from values
    // Consider adding `isArray` option if you need to consistently treat certain tags as arrays
    // Example: isArray: (tagName, jPath, isLeafNode, isAttribute) => tagName === 'item' 
  };

  const parser = new XMLParser(parserOpts);
  let parsedObj: Record<string, unknown>;
  try {
    parsedObj = parser.parse(xmlStr) as Record<string, unknown>;
  } catch (error) {
    console.error("Error parsing XML:", error);
    // Return an empty array or a specific error structure if parsing fails
    return [{ error: "Invalid XML format" }]; 
  }


  // --- 3. Recursively flatten the object
  const rows: Record<string, string>[] = [];

  function walk(
    node: unknown,
    path: string,
    currentRow: Record<string, string>
  ) {
    if (node === null || node === undefined) return;

    // 3.1 If it's a primitive value, assign it to the current row with its path
    if (typeof node !== 'object') {
      currentRow[path] = String(node);
      return;
    }

    // 3.2 If it's an array, iterate through its elements
    if (Array.isArray(node)) {
      node.forEach((item, idx) => {
        // Each element in the array can potentially start a new row or extend the current one.
        // For simplicity in this columnar conversion, if an array item is an object,
        // it often implies multiple records.
        // If the item itself is a complex object, it might be better to create a new row.
        // If it's an array of primitives, they could be joined or handled based on specific needs.
        
        // Create a new row for subsequent array items to represent them as distinct records
        const rowCopy = idx === 0 ? currentRow : { ...currentRow }; 
        walk(item, path, rowCopy); // The path remains the same for items in an array
        if (idx !== 0 && Object.keys(rowCopy).length > Object.keys(currentRow).length) {
           // Only push if rowCopy actually got new data from this array item
           // and it's not the first item (which uses the original currentRow)
          rows.push(rowCopy);
        } else if (idx === 0 && Object.keys(rowCopy).length === 0 && rows.length === 0) {
          // Special case: if the very first walk on the first array item results in an empty object,
          // it means the array might contain simple primitives.
          // This part might need refinement based on expected XML structures.
        }
      });
      // If the original currentRow (for the first item) was modified, ensure it's added if not already.
      // This logic is tricky because the first item modifies currentRow directly.
      // The goal is to ensure that if the array processing splits into multiple conceptual "records",
      // they are all captured.
      return;
    }

    // 3.3 If it's an object, iterate through its key-value pairs
    for (const [k, v] of Object.entries(node as Record<string, unknown>)) {
      const tag = lowerCaseTags ? k.toLowerCase() : k;
      if (ignoreTags.includes(tag)) continue; // Skip ignored tags
      
      // Construct the next path for the current key
      const nextPath = path ? `${path}${joiner}${tag}` : tag;
      walk(v, nextPath, currentRow);
    }
  }

  // --- 4. Start flattening process
  const rootRow: Record<string, string> = {};
  walk(parsedObj, '', rootRow);
  
  // Add the initial rootRow if it contains data or if no other rows were generated by arrays
  if (Object.keys(rootRow).length > 0) {
    rows.unshift(rootRow);
  } else if (rows.length === 0 && Object.keys(rootRow).length === 0 && xmlStr.trim() !== '') {
    // Handle cases where XML might be valid but results in no data after filtering/processing
    // e.g. an XML with only a root tag that gets stripped or contains only ignored tags.
    // Or if the root itself was an array processed by walk.
    // If rootRow is empty but rows got populated by array processing, that's fine.
    // If both are empty, it might mean the XML was empty or unparseable into the desired structure.
    // For now, if rootRow is empty and rows is also empty, but there was XML content,
    // we might add an empty object to signify processing occurred but yielded no columns.
    // This behavior might need adjustment based on how "empty" results should be handled.
    // For now, if rows is empty and rootRow is empty, we'll let rows be empty.
  }


  // Deduplicate rows that might be identical if an array contained identical objects
  // This is a simple deduplication based on JSON stringification. More robust methods exist.
  if (rows.length > 1) {
    const uniqueRows = Array.from(new Set(rows.map(row => JSON.stringify(row)))).map(str => JSON.parse(str));
    return uniqueRows;
  }

  return rows;
}

/* ---------- Example Usage (for testing in Node.js environment) ---------- */
// This part is for direct execution of this file (e.g., `node xmlUtils.js`)
// It won't run when imported as a module in a React app.
// Commenting out to prevent linting errors in a browser-focused project.
/*
if (typeof require !== 'undefined' && require.main === module) {
  const fs = require('fs');
  const path = require('path');

  // Example: Load and parse a sample XML file
  try {
    const sampleXmlPath = path.join(__dirname, '..', '..', 'sample', '238.xml'); // Adjust path as needed
    if (fs.existsSync(sampleXmlPath)) {
      const xml = fs.readFileSync(sampleXmlPath, 'utf8');
      console.log("--- Parsing 238.xml ---");
      const csvRows = xmlToRows(xml);
      
      if (csvRows.length > 0) {
        console.log("Number of rows:", csvRows.length);
        console.log("Columns in the first row:", Object.keys(csvRows[0]));
        console.log("First row data:", csvRows[0]);
        if (csvRows.length > 1) {
          console.log("Second row data (if exists):", csvRows[1]);
        }
      } else {
        console.log("No data rows generated from XML.");
      }
    } else {
      console.log(`Sample XML file not found at: ${sampleXmlPath}`);
    }
  } catch (e) {
    console.error("Error during example usage:", e);
  }
}
*/
