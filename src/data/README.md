# Validation Data

This directory contains sample validation rules and reference data for invoice validation.

## Files

### `sampleValidationRules.ts`
Contains sample validation rules for testing the invoice validation system. Each rule includes:
- `taxCode`: Company tax identification number
- `name`: Company name
- `address`: Company address (supports Vietnamese address abbreviations)

## Usage

In a production environment, this data would typically come from:
- Database queries
- External APIs
- Configuration files
- Government tax databases

## Address Abbreviation Support

The validation system supports common Vietnamese address abbreviations:
- **Phường** → P, P.
- **Thị trấn** → TT, TT.
- **Quận** → Q, Q.
- **Thị xã** → TX, TX.
- **Thành phố** → TP, TP.
- **Việt Nam** → VN

## Adding New Validation Rules

To add new validation rules, simply add new objects to the `SAMPLE_VALIDATION_RULES` array:

```typescript
{
  taxCode: "*********",
  name: "COMPANY NAME",
  address: "Full company address"
}
```

## Data Structure

Each validation rule follows the `ValidationRule` interface defined in `src/types/validation.ts`.
