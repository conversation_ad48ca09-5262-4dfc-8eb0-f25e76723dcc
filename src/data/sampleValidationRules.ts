import type { ValidationRule } from "@/types/validation";

/**
 * Sample validation rules for testing
 * In a real application, this data would come from a database or API
 */
export const SAMPLE_VALIDATION_RULES: ValidationRule[] = [
  {
    taxCode: "*********",
    name: "CÔNG TY CỔ PHẦN TTMI",
    address:
      "146 Bình Lợi, Phường 13, Quận Bình Thạnh, T<PERSON><PERSON><PERSON><PERSON><PERSON>, Việt Nam",
  },
  {
    taxCode: "*********",
    name: "CÔNG TY CỔ PHẦN THƯƠNG MẠI ĐẦU TƯ SK",
    address:
      "148 B<PERSON>nh Lợi, Phường 13, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Việt Nam",
  },
  {
    taxCode: "*********",
    name: "CÔNG TY CỔ PHẦN THƯƠNG MẠI - DỊCH VỤ - SẢN XUẤT 3T",
    address:
      "148 Bình Lợi, Phường 13, <PERSON><PERSON><PERSON><PERSON>nh, <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, Việt Nam",
  }
];
