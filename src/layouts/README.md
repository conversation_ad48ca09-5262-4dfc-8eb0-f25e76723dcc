# Layouts

This directory contains layout components that define the overall structure and organization of pages in the Folder Explorer application.

## Structure

```
layouts/
└── MainLayout.tsx      # Primary application layout
```

## Layout Components

### MainLayout.tsx

The main application layout component that provides the overall structure for the entire application.

**Features**:
- Resizable panel layout using Shadcn/ui Resizable components
- Left sidebar for navigation (20% default width, 15-30% range)
- Main content area for pages (80% default width)
- Responsive design that adapts to different screen sizes
- Proper integration of navigation and page content

**Structure**:
```tsx
<div className="h-screen w-full">
  <ResizablePanelGroup direction="horizontal">
    <ResizablePanel> {/* Sidebar */}
      <SideNav />
    </ResizablePanel>
    <ResizableHandle />
    <ResizablePanel> {/* Main Content */}
      <FolderDropPage />
    </ResizablePanel>
  </ResizablePanelGroup>
</div>
```

**Dependencies**:
- `@/components/ui/resizable` - For resizable panel functionality
- `@/components/SideNav` - Navigation sidebar
- `@/pages/FolderDropPage` - Main application page

## Usage

### Basic Implementation

```tsx
import MainLayout from "@/layouts/MainLayout"

function App() {
  return <MainLayout />
}
```

### With Routing (Future Enhancement)

If you want to extend this layout to support multiple pages with routing:

```tsx
import { BrowserRouter, Routes, Route } from "react-router-dom"
import MainLayout from "@/layouts/MainLayout"

function App() {
  return (
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<MainLayout />} />
        {/* Add more routes as needed */}
      </Routes>
    </BrowserRouter>
  )
}
```

## Design Principles

- **Responsive**: Layout adapts to different screen sizes
- **Flexible**: Resizable panels allow users to customize their workspace
- **Consistent**: Provides a consistent structure across the application
- **Accessible**: Maintains proper semantic structure and keyboard navigation
- **Performance**: Optimized for smooth resizing and rendering

## Customization

### Panel Sizes

You can modify the default panel sizes in `MainLayout.tsx`:

```tsx
<ResizablePanel 
  defaultSize={25}  // Change default sidebar width (percentage)
  minSize={20}      // Minimum sidebar width
  maxSize={40}      // Maximum sidebar width
>
```

### Adding New Layouts

To create additional layouts:

1. Create a new file in `src/layouts/`
2. Follow the same pattern as `MainLayout.tsx`
3. Import and use your layout components in pages or routing

Example:
```tsx
// src/layouts/FullPageLayout.tsx
export default function FullPageLayout({ children }) {
  return (
    <div className="h-screen w-full p-4">
      {children}
    </div>
  )
}
