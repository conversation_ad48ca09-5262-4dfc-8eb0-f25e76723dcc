import { useState } from 'react';
import { loadPaymentMethodMapping } from '@/utils/InvoicePage/helpers/paymentMethodMapping';

export function usePaymentMapping() {
  const [paymentMapping, setPaymentMapping] = useState<Map<string, string> | null>(null);
  const [mappingError, setMappingError] = useState<string | null>(null);

  const handleMappingFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setMappingError(null);

    try {
      const mapping = await loadPaymentMethodMapping(file);
      setPaymentMapping(mapping);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Lỗi đọc file mapping';
      setMappingError(errorMessage);
    } finally {
      event.target.value = '';
    }
  };

  return {
    paymentMapping,
    mappingError,
    handleMappingFileUpload,
    setMappingError
  };
}
