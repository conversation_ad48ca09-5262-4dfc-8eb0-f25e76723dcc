import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

import { FileText, Upload, Download, FileSpreadsheet, Settings } from 'lucide-react';

import { processD05File, processARITOFile, downloadProcessedData, downloadDirectARITOData } from '@/utils/InvoicePage';
import type { ARITORecord } from '@/utils/InvoicePage/processors/aritoProcessor';
import { Label } from '@/components/ui/label';
import type { ProcessedRecord } from '@/utils/InvoicePage/types';
import { usePaymentMapping } from './hooks/usePaymentMapping';



export default function InvoicePage() {
  const [error, setError] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processedData, setProcessedData] = useState<ProcessedRecord[] | null>(null);
  const [aritoData, setAritoData] = useState<ARITORecord[] | null>(null);
  const [d05File, setD05File] = useState<File | null>(null);
  const [htFile, setHtFile] = useState<File | null>(null);

  const { paymentMapping, mappingError, handleMappingFileUpload } = usePaymentMapping();

  const handleD05FileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setD05File(file);
    event.target.value = '';
  };

  const handleHTFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setHtFile(file);
    event.target.value = '';
  };

  const handleProcessFiles = async () => {
    if (!d05File || !htFile) {
      setError('Vui lòng tải lên cả file D05-IPOS và file HT lookup');
      return;
    }

    if (!paymentMapping) {
      setError('Vui lòng tải lên file gộp đơn trước khi xử lý file D05-IPOS');
      return;
    }

    setIsProcessing(true);
    setError(null);
    setProcessedData(null);

    try {
      const processedData = await processD05File(d05File, htFile);
      setProcessedData(processedData);

      const aritoRecords = await processARITOFile(processedData, paymentMapping);
      setAritoData(aritoRecords);
      console.log(aritoRecords);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Lỗi xử lý file';
      setError(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownloadProcessed = () => {
    if (processedData) downloadProcessedData(processedData, 'XU_LY_D05_RESULT.xlsx');
  };

  const handleDownloadARITO = () => {
    if (aritoData) downloadDirectARITOData(aritoData, 'ARITO_DATA.xlsx');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <div className="flex items-center gap-3">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Trang Hóa Đơn
              </h1>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Gộp đơn file (Bắt buộc)
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Tải lên file Excel gộp đơn để ánh xạ phương thức thanh toán sang mã khách hàng
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  className="relative"
                  asChild
                >
                  <Label>
                    <Upload className="h-4 w-4 mr-2" />
                    Chọn file gộp đơn.xlsx
                    <input
                      type="file"
                      accept=".xlsx,.xls"
                      onChange={handleMappingFileUpload}
                      className="absolute inset-0 opacity-0 cursor-pointer"
                      hidden
                    />
                  </Label>
                </Button>

                {paymentMapping && (
                  <div className="text-sm text-green-600">
                    ✓ Đã tải file gộp đơn
                  </div>
                )}
              </div>

              {mappingError && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{mappingError}</p>
                </div>
              )}

              <div className="text-xs text-gray-500">
                <p><strong>Format file Excel gộp đơn:</strong></p>
                <ul className="list-disc list-inside mt-1">
                  <li>Dòng 1: Headers (Công ty, Phương thức thanh toán, Mã khách)</li>
                  <li>Dòng 2: Bỏ qua</li>
                  <li>Dòng 3: Dữ liệu gộp đơn (VD: TTM, BE-DHI, BE)</li>
                </ul>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileSpreadsheet className="h-5 w-5" />
                Xử lý file D05-IPOS
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Tải lên file D05-IPOS.xlsx và file HT lookup để tự động xử lý và tải xuống file XU_LY_D05.xlsx hoặc ARITO.xlsx
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium">File D05-IPOS Data</Label>
                  <Button
                    variant="outline"
                    disabled={isProcessing}
                    className="relative w-full"
                    asChild
                  >
                    <Label>
                      <Upload className="h-4 w-4 mr-2" />
                      {d05File ? d05File.name : 'Chọn file D05-IPOS.xlsx'}
                      <input
                        type="file"
                        accept=".xlsx,.xls"
                        onChange={handleD05FileUpload}
                        className="absolute inset-0 opacity-0 cursor-pointer"
                        hidden
                      />
                    </Label>
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm font-medium">File HT Lookup</Label>
                  <Button
                    variant="outline"
                    disabled={isProcessing}
                    className="relative w-full"
                    asChild
                  >
                    <Label>
                      <Upload className="h-4 w-4 mr-2" />
                      {htFile ? htFile.name : 'Chọn file HT lookup.xlsx'}
                      <input
                        type="file"
                        accept=".xlsx,.xls"
                        onChange={handleHTFileUpload}
                        className="absolute inset-0 opacity-0 cursor-pointer"
                        hidden
                      />
                    </Label>
                  </Button>
                </div>
              </div>

              <div className="flex items-center gap-4">
                <Button
                  onClick={handleProcessFiles}
                  disabled={isProcessing || !d05File || !htFile}
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  {isProcessing ? 'Đang xử lý...' : 'Xử lý files'}
                </Button>

                {processedData && (
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={handleDownloadProcessed}
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      Tải xuống XU_LY_D05.xlsx
                    </Button>
                    {aritoData && (
                      <Button
                        onClick={handleDownloadARITO}
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        <Download className="h-4 w-4" />
                        Tải xuống ARITO.xlsx
                      </Button>
                    )}
                  </div>
                )}
              </div>

              {error && (
                <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Hướng dẫn sử dụng</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600">
                <h4 className="font-medium mb-2">Format file Excel yêu cầu:</h4>
                <div className="bg-blue-50 p-3 rounded-md mb-3">
                  <p className="font-medium text-blue-800 mb-2">Cần 2 file Excel riêng biệt:</p>
                  <ul className="list-disc list-inside space-y-1 text-blue-700">
                    <li><strong>File D05-IPOS</strong> - File chứa dữ liệu chính để xử lý</li>
                    <li><strong>File HT Lookup</strong> - File chứa dữ liệu mapping (Cửa hàng, PTTT, mã kho, mã bộ phận, tk nợ)</li>
                  </ul>
                </div>

                <h4 className="font-medium mb-2">Gộp đơn file (Bắt buộc):</h4>
                <div className="bg-green-50 p-3 rounded-md mb-3">
                  <p className="font-medium text-green-800 mb-2">File Excel gộp đơn có cấu trúc:</p>
                  <ul className="list-disc list-inside space-y-1 text-green-700">
                    <li><strong>Dòng 1:</strong> Headers (Công ty, Phương thức thanh toán, Mã khách)</li>
                    <li><strong>Dòng 2:</strong> Bỏ qua (skip)</li>
                    <li><strong>Dòng 3:</strong> Dữ liệu gộp đơn (VD: TTM, BE-DHI, BE)</li>
                  </ul>
                  <p className="text-sm mt-2 text-green-600">
                    <strong>Ví dụ:</strong> Khi PTTT = "BE-DHI" sẽ được gộp thành Mã khách hàng = "BE"
                  </p>
                </div>

                <h4 className="font-medium mb-2">Quy trình xử lý:</h4>
                <ul className="list-disc list-inside space-y-1">
                  <li><strong>Bước 1:</strong> Tải lên file gộp đơn (bắt buộc) để ánh xạ phương thức thanh toán</li>
                  <li><strong>Bước 2:</strong> Tải lên file D05-IPOS data</li>
                  <li><strong>Bước 3:</strong> Tải lên file HT lookup</li>
                  <li><strong>Bước 4:</strong> Nhấn nút "Xử lý files"</li>
                  <li>Hệ thống đọc dữ liệu từ file D05-IPOS</li>
                  <li>Sử dụng file HT để mapping thông tin cửa hàng</li>
                  <li>Áp dụng gộp đơn phương thức thanh toán từ file đã tải</li>
                  <li>Nhóm dữ liệu theo: Ngày, Cửa hàng, PTTT, Mã hàng, Giá bán</li>
                  <li>Tính tổng: Số lượng, Thành tiền, Giảm giá, Chiết khấu</li>
                  <li>Tính Giá bán = Thành tiền ÷ Số lượng</li>
                  <li>Có thể tải xuống 2 định dạng:</li>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li><strong>XU_LY_D05.xlsx</strong> - Định dạng xử lý thông thường</li>
                    <li><strong>ARITO.xlsx</strong> - Định dạng ARITO với mã khách hàng được gộp đơn</li>
                  </ul>
                </ul>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
