import { useState } from "react";
import { useFolderDrop } from "@/hooks/useFolderDrop";
import { FolderPageHeader } from "@/components/folderDropPage/FolderPageHeader";
import { FolderDropZone } from "@/components/folderDropPage/FolderDropZone";
import { FileDisplayTable } from "@/components/folderDropPage/FileDisplayTable";
import { JsonViewer } from "@/components/folderDropPage/JsonViewer"; // Changed from XmlDataViewer
import { TaxRateConfig } from "@/components/folderDropPage/TaxRateConfig";
import type { FileEntry } from "@/types/fileSystem";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "@/components/ui/resizable";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ValidationViewer } from "@/components/folderDropPage/ValidationViewer";

export default function FolderDropPage() {
  const [selectedFileForView, setSelectedFileForView] =
    useState<FileEntry | null>(null);
  const {
    files,
    isDragOver,
    isProcessing,
    selectedCompany,
    companies,
    fileInputRef,
    xmlFileInputRef,
    handleDrop,
    handleFileSelect,
    handleXmlFileSelect,
    handleDragOver,
    handleDragLeave,
    clearFiles,
    handleCompanyChange,
  } = useFolderDrop();

  const handleFileSelection = (file: FileEntry) => {
    if (file.name.toLowerCase().endsWith(".xml")) {
      // For XML files, we expect jsonData to be populated (or have an error)
      setSelectedFileForView(file);
    } else {
      setSelectedFileForView(null); // Clear selection for non-XML files
    }
  };

  const handleClearFiles = () => {
    clearFiles();
    setSelectedFileForView(null);
  };

  return (
    <div className="h-full w-full bg-background flex flex-col p-4 sm:p-6 overflow-y-auto">
      <FolderPageHeader
        onChooseFolderClick={() => fileInputRef.current?.click()}
        onChooseXmlFileClick={() => xmlFileInputRef.current?.click()}
        onClearFilesClick={handleClearFiles}
        hasFiles={files.length > 0}
        companies={companies}
        selectedCompany={selectedCompany}
        onCompanyChange={handleCompanyChange}
      />

      {/* Hidden input for folder selection */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileSelect}
        className="hidden"
        // @ts-expect-error for non-standard attributes
        webkitdirectory=""
        directory=""
        multiple
      />

      {/* Hidden input for XML file selection */}
      <input
        type="file"
        ref={xmlFileInputRef}
        onChange={handleXmlFileSelect}
        className="hidden"
        accept=".xml"
        multiple
      />

      {/* Initial State - Show only when no files */}
      {files.length === 0 && (
        <div className="flex-1 flex flex-col mt-6 space-y-6">
          <FolderDropZone
            isDragOver={isDragOver}
            isProcessing={isProcessing}
            fileCount={files.length}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          />

          {/* Tax Rate Configuration */}
          <TaxRateConfig className="max-w-4xl mx-auto" />
        </div>
      )}

      {/* File Management State - Show when files are loaded */}
      {files.length > 0 && (
        <>
          {/* Desktop Layout */}
          <div className="hidden lg:block flex-1 mt-6">
            <ResizablePanelGroup
              direction="horizontal"
              className="h-full border rounded-lg border-border shadow-sm"
            >
              <ResizablePanel defaultSize={50} minSize={30}>
                <FileDisplayTable
                  files={files}
                  onFileSelect={handleFileSelection}
                  selectedFilePath={selectedFileForView?.path}
                  selectedCompany={selectedCompany}
                />
              </ResizablePanel>
              <ResizableHandle withHandle />
              <ResizablePanel defaultSize={50} minSize={30}>
                <Card className="h-full flex flex-col">
                  <CardHeader className="py-4">
                    <CardTitle className="text-responsive-md">
                      {selectedFileForView?.name
                        ? `Xem chi tiết: ${selectedFileForView.name}`
                        : "Trình xem chi tiết"}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-1 p-0 overflow-hidden">
                    {selectedFileForView &&
                    selectedFileForView.name.toLowerCase().endsWith(".xml") &&
                    selectedFileForView.jsonData ? (
                      <Tabs
                        defaultValue="validation"
                        className="h-full flex flex-col"
                      >
                        <div className="px-4 pt-2 pb-0">
                          <TabsList className="w-full h-9">
                            <TabsTrigger value="validation" className="text-sm">
                              Kiểm tra
                            </TabsTrigger>
                            <TabsTrigger value="json" className="text-sm">
                              JSON
                            </TabsTrigger>
                          </TabsList>
                        </div>
                        <TabsContent
                          value="validation"
                          className="flex-1 overflow-hidden mt-0"
                        >
                          {selectedFileForView.validationResult ? (
                            <ValidationViewer
                              validationResult={
                                selectedFileForView.validationResult
                              }
                              selectedCompany={selectedCompany}
                            />
                          ) : (
                            <div className="p-6 text-center text-muted-foreground h-full flex items-center justify-center">
                              <p className="text-responsive-sm">
                                Không có kết quả kiểm tra cho file này.
                              </p>
                            </div>
                          )}
                        </TabsContent>
                        <TabsContent
                          value="json"
                          className="flex-1 overflow-hidden mt-0"
                        >
                          <JsonViewer
                            jsonData={selectedFileForView.jsonData}
                            fileName={selectedFileForView.name}
                          />
                        </TabsContent>
                      </Tabs>
                    ) : (
                      <div className="p-6 text-center text-muted-foreground h-full flex items-center justify-center">
                        <p className="text-responsive-sm">
                          Chọn một tệp XML từ danh sách để xem chi tiết ở đây.
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </ResizablePanel>
            </ResizablePanelGroup>
          </div>

          {/* Mobile/Tablet Layout */}
          <div className="lg:hidden flex-1 mt-6 space-y-4">
            <FileDisplayTable
              files={files}
              onFileSelect={handleFileSelection}
              selectedFilePath={selectedFileForView?.path}
              selectedCompany={selectedCompany}
            />

            {selectedFileForView &&
              selectedFileForView.name.toLowerCase().endsWith(".xml") &&
              selectedFileForView.jsonData && (
                <Card className="min-h-[300px] flex flex-col">
                  <CardHeader className="py-4">
                    <CardTitle className="text-responsive-md">
                      Chi tiết: {selectedFileForView.name}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="flex-1 p-0 overflow-hidden">
                    <Tabs
                      defaultValue="validation"
                      className="h-full flex flex-col"
                    >
                      <div className="px-4 pt-2 pb-0">
                        <TabsList className="w-full h-9">
                          <TabsTrigger value="validation" className="text-sm">
                            Kiểm tra
                          </TabsTrigger>
                          <TabsTrigger value="json" className="text-sm">
                            JSON
                          </TabsTrigger>
                        </TabsList>
                      </div>
                      <TabsContent
                        value="validation"
                        className="flex-1 overflow-hidden mt-0"
                      >
                        {selectedFileForView.validationResult ? (
                          <ValidationViewer
                            validationResult={
                              selectedFileForView.validationResult
                            }
                            selectedCompany={selectedCompany}
                          />
                        ) : (
                          <div className="p-6 text-center text-muted-foreground h-full flex items-center justify-center">
                            <p className="text-responsive-sm">
                              Không có kết quả kiểm tra cho file này.
                            </p>
                          </div>
                        )}
                      </TabsContent>
                      <TabsContent
                        value="json"
                        className="flex-1 overflow-hidden mt-0"
                      >
                        <JsonViewer
                          jsonData={selectedFileForView.jsonData}
                          fileName={selectedFileForView.name}
                        />
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </Card>
              )}
          </div>
        </>
      )}
    </div>
  );
}
