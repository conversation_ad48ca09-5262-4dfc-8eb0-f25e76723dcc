import { FileSpreadsheet } from 'lucide-react';

export default function Header() {
  return (
    <div className="flex-shrink-0 border-b bg-card">
      <div className="p-4 sm:p-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <FileSpreadsheet className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Phân tích file Excel</h1>
            <p className="text-sm text-muted-foreground">
              Tải lên và phân tích dữ liệu từ file Excel theo định dạng chuẩn
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
