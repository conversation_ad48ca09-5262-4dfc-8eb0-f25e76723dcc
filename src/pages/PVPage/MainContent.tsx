import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import ExcelInput from '@/components/ExcelInput';
import CustomerSelector from '@/components/CustomerSelector';
import OrderSummaryTable from '@/components/OrderSummaryTable';
import FileFormatRequirements from '@/components/FileFormatRequirements';
import type { ExcelParseResult } from '@/types/excel';
import type { OrderSummaryResult } from '@/utils/orderSummaryUtils';
import { createOrderSummary } from '@/utils/orderSummaryUtils';
import { getUnitConversionFromLocalStorage, hasUnitConversionData } from '@/utils/unitConversionUtils';


export default function MainContent() {
  const [parseResult, setParseResult] = useState<ExcelParseResult | null>(null);
  const [error, setError] = useState<string>('');
  const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
  const [orderSummary, setOrderSummary] = useState<OrderSummaryResult | null>(null);
  const [orderSummaryError, setOrderSummaryError] = useState<string>('');
  const [exportDate, setExportDate] = useState<string>('');

  const handleParseSuccess = (result: ExcelParseResult) => {
    setParseResult(result);
    setError('');
    setSelectedCustomers([]);
    setOrderSummary(null);
  };

  const handleParseError = (errorMessage: string) => {
    setError(errorMessage);
    setParseResult(null);
    setSelectedCustomers([]);
    setOrderSummary(null);
  };

  const handleConversionSuccess = () => {

  };

  const handleConversionError = () => {

  };

  const handleReset = () => {
    setParseResult(null);
    setError('');
    setSelectedCustomers([]);
    setOrderSummary(null);
  };

  const handleCustomerSelectionChange = (customers: string[]) => {
    setSelectedCustomers(customers);
    setOrderSummary(null);
  };

  const handleExportDateChange = (date: string) => {
    setExportDate(date);
  };

  const handleOrderSummary = (customers: string[], exportDate?: string) => {
    if (!parseResult || customers.length === 0) return;

    if (!hasUnitConversionData()) {
      setOrderSummaryError('Không tìm thấy dữ liệu đơn vị tính. Vui lòng import file Excel đơn vị tính trước khi tạo tổng hợp đơn hàng.');
      return;
    }

    setOrderSummaryError('');
    const unitConversionData = getUnitConversionFromLocalStorage();
    const conversionData = unitConversionData.map(item => ({
      materialCode: item.ma_vt,
      materialName: '',
      unit: item.dvt,
      conversionFactor: item.he_so,
      rowNumber: 0,
    }));

    const summary = createOrderSummary(parseResult, customers, exportDate, conversionData, parseResult.metadata.parsedAt);
    if (exportDate) {
      (summary as any).exportDate = exportDate;
    }
    setOrderSummary(summary);
    setError('');
  };

  const handleCloseOrderSummary = () => {
    setOrderSummary(null);
  };

  const handleUnitConversionUpdate = () => {
    setOrderSummaryError('');

    if (orderSummary && parseResult && selectedCustomers.length > 0) {
      const unitConversionData = getUnitConversionFromLocalStorage();
      const conversionData = unitConversionData.map(item => ({
        materialCode: item.ma_vt,
        materialName: '',
        unit: item.dvt,
        conversionFactor: item.he_so,
        rowNumber: 0,
      }));

      const updatedSummary = createOrderSummary(parseResult, selectedCustomers, exportDate, conversionData, parseResult.metadata.parsedAt);
      if (exportDate) {
        (updatedSummary as any).exportDate = exportDate;
      }
      setOrderSummary(updatedSummary);
    }
  };
  
  return (
    <div className="flex-1 overflow-auto">
      <div className="p-4 sm:p-6 space-y-6">
        {!parseResult && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Tải lên file Excel - Dữ liệu chính</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Chọn file Excel để phân tích dữ liệu theo quy tắc định sẵn
                  </p>
                </CardHeader>
                <CardContent>
                  <ExcelInput
                    type="excel"
                    onSuccess={handleParseSuccess}
                    onError={handleParseError}
                    options={{
                      headerRow: 8,
                      dataStartRow: 10,
                      trimValues: true,
                      skipEmptyRows: true,
                    }}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Tải lên file Excel - Đơn vị tính</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Chọn file Excel chứa dữ liệu đơn vị tính và hệ số chuyển đổi
                  </p>
                </CardHeader>
                <CardContent>
                  <ExcelInput
                    type="unit-conversion"
                    onSuccess={handleConversionSuccess}
                    onError={handleConversionError}
                  />
                </CardContent>
              </Card>
            </div>

            <FileFormatRequirements />
          </div>
        )}

        {parseResult && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Dữ liệu đã phân tích</h2>
              <Button onClick={handleReset} variant="outline">
                Tải file mới
              </Button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <CustomerSelector
                  result={parseResult}
                  onSelectionChange={handleCustomerSelectionChange}
                  onOrderSummary={handleOrderSummary}
                  exportDate={exportDate}
                  onExportDateChange={handleExportDateChange}
                  orderSummaryError={orderSummaryError}
                />
              </div>
              <div className="lg:col-span-1">
                <Card>
                  <CardHeader>
                    <CardTitle>Import Đơn vị tính</CardTitle>
                    <p className="text-sm text-muted-foreground">
                      Tải lên file Excel chứa dữ liệu đơn vị tính và hệ số chuyển đổi
                    </p>
                  </CardHeader>
                  <CardContent>
                    <ExcelInput
                      type="unit-conversion"
                      onSuccess={handleConversionSuccess}
                      onError={handleConversionError}
                      onLocalStorageUpdate={handleUnitConversionUpdate}
                    />

                    <div className="text-xs text-muted-foreground space-y-1 mt-4">
                      <div className="font-medium">Yêu cầu định dạng file:</div>
                      <ul className="list-disc list-inside space-y-1 ml-2">
                        <li>Dòng 8: Tiêu đề cột (Mã vật tư, Tên vật tư, Đvt, Hệ số)</li>
                        <li>Dòng 9+: Dữ liệu đơn vị tính</li>
                        <li>Cột "Mã vật tư": Mã định danh vật tư</li>
                        <li>Cột "Hệ số": Hệ số quy đổi đơn vị</li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}

        {orderSummary && (
          <div className="space-y-4">
            <OrderSummaryTable
              summary={orderSummary}
              onClose={handleCloseOrderSummary}
            />
          </div>
        )}

        {error && !parseResult && (
          <Card className="border-destructive">
            <CardContent className="p-4">
              <div className="text-destructive">
                <strong>Lỗi:</strong> {error}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
