# Pages

This directory contains the main page components that represent different views and functionality in the Folder Explorer application.

## Structure

```
pages/
└── FolderDropPage.tsx  # Main drag & drop folder explorer page
```

## Page Components

### FolderDropPage.tsx

The primary page component that implements the core folder drag-and-drop functionality. *(Note: The application user interface is in Vietnamese.)*

**Features**:
- Drag & drop interface for folders and files.
- **"Chọn thư mục / tệp" (Choose Folder/Files) button** for traditional file dialog selection.
- File System Access API integration (for drag & drop) with fallback support.
- Standard file input handling (for button selection), including `webkitdirectory` for folder selection.
- Interactive file listing table with sorting capabilities
- Visual feedback during drag operations
- File size formatting and type detection
- Error handling and user feedback
- Responsive design for all screen sizes

**Key Functionality**:

#### State Management
- `files`: Array of discovered files and directories
- `isDragOver`: Visual feedback for drag operations
- `isProcessing`: Loading state during file processing

#### File Processing
- **Drag & Drop**:
    - Modern browsers: Uses File System Access API for full folder support.
    - Legacy browsers: Fallback to basic file drag & drop.
- **Choose <PERSON>**:
    - Uses `<input type="file" webkitdirectory directory multiple>`.
    - Processes `FileList` and attempts to infer folder structure from `webkitRelativePath`.
- **Recursive scanning (Drag & Drop)**: Traverses subdirectories to build complete file tree.
- **Type detection**: Distinguishes between files and directories.

#### User Interface
- **Drop Zone**: Large, interactive area for dropping folders.
- **"Chọn thư mục / tệp" (Choose Folder / Files) Button**: Provides an alternative input method.
- **File Table**: Sortable table showing file details.
- **Clear Function**: Reset functionality to start over
- **Loading States**: Visual feedback during processing

**File Structure Output**:
```typescript
interface FileEntry {
  name: string        // File or directory name
  path: string        // Relative path from dropped folder
  type: 'file' | 'directory'  // Entry type
  size?: number       // File size in bytes (files only)
}
```

## Usage Examples

### Direct Usage
```tsx
import FolderDropPage from "@/pages/FolderDropPage"

export default function App() {
  return <FolderDropPage />
}
```

### Within Layout
```tsx
import MainLayout from "@/layouts/MainLayout"
import FolderDropPage from "@/pages/FolderDropPage"

export default function App() {
  return (
    <MainLayout>
      <FolderDropPage />
    </MainLayout>
  )
}
```

## Technical Implementation

### File System Access API (Drag & Drop)

For drag & drop operations, the page uses the modern File System Access API when available:

```typescript
// Check for API support
if ('getAsFileSystemHandle' in item && item.getAsFileSystemHandle) {
  const handle = await item.getAsFileSystemHandle()
  // Process directory or file handle
}
```

### Recursive Directory Processing

```typescript
async function getFilePaths(directoryHandle: FileSystemDirectoryHandle) {
  const filePaths: FileEntry[] = []
  
  for await (const [name, handle] of directoryHandle.entries()) {
    if (handle.kind === 'file') {
      // Process file
    } else if (handle.kind === 'directory') {
      // Recursively process subdirectory
      const subFiles = await getFilePaths(handle)
      filePaths.push(...subFiles)
    }
  }
  
  return filePaths
}
```

### Error Handling

- Browser compatibility detection
- File access permission errors
- Invalid file/folder handling
- User-friendly error messages

## Browser Compatibility

| Feature | Browser | Support Level | Notes |
|---------|---------|---------------|-------|
| Drag & Drop (Folder) | Chrome 86+, Edge 86+, Opera 72+ | Full | Uses File System Access API |
| Drag & Drop (Files) | Most modern browsers | Full |  |
| Choose Button (Folder) | Chrome, Edge, Opera | Full | Uses `webkitdirectory` |
| Choose Button (Multiple Files) | Most modern browsers | Full |  |
| Choose Button (Single File) | All modern browsers | Full |  |

## Future Enhancements

Potential improvements for this page:

1. **File Filtering**: Add search/filter functionality
2. **File Preview**: Quick preview for supported file types
3. **Export Options**: Save file lists as CSV/JSON
4. **Sorting**: Multiple column sorting options
5. **File Operations**: Copy/move/delete operations
6. **Folder Structure View**: Tree view of directory structure
7. **File Analytics**: Size distribution, file type analysis

## Dependencies

- `@/components/ui/*` - Shadcn/ui components
- `lucide-react` - Icons for file types and UI
- `react` - Core hooks (useState, useCallback)

## Performance Considerations

- **Large Directories**: Handles thousands of files efficiently
- **Memory Management**: Streams directory contents to avoid memory issues
- **UI Responsiveness**: Uses async processing to prevent UI blocking
- **File Size Formatting**: Optimized display of file sizes
