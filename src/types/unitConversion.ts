/**
 * Unit conversion data types and interfaces
 */

export interface UnitConversionData {
  /** Material code (Mã vật tư) */
  materialCode: string;
  /** Material name (Tê<PERSON> vật tư) */
  materialName: string;
  /** Unit (Đvt) */
  unit: string;
  /** Conversion factor (<PERSON><PERSON> số) */
  conversionFactor: number;
  /** Row number for reference */
  rowNumber: number;
}

export interface UnitConversionResult {
  /** Parsed conversion data */
  data: UnitConversionData[];
  /** Original filename */
  filename: string;
  /** Total number of data rows parsed */
  dataRowCount: number;
  /** Parsing metadata */
  metadata: {
    /** When the file was parsed */
    parsedAt: Date;
    /** File size in bytes */
    fileSize: number;
    /** Sheet name that was parsed */
    sheetName: string;
  };
}

export interface UnitConversionError {
  /** Error type */
  type: 'FILE_READ_ERROR' | 'INVALID_FORMAT' | 'MISSING_HEADER' | 'PARSING_ERROR';
  /** Human-readable error message */
  message: string;
  /** Technical details for debugging */
  details?: string;
  /** Row number where error occurred (if applicable) */
  rowNumber?: number;
}
