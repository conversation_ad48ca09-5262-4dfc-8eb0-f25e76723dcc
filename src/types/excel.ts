/**
 * Excel file processing types and interfaces
 */

export interface ExcelHeader {
  /** Column index (0-based) */
  index: number;
  /** Column name from header row */
  name: string;
  /** Original column letter (A, B, C, etc.) */
  column: string;
}

export interface ExcelRow {
  /** Row number (1-based, matching Excel) */
  rowNumber: number;
  /** Raw data values indexed by column */
  data: Record<string, any>;
  /** Processed data with header names as keys */
  values: Record<string, any>;
}

export interface ExcelParseResult {
  /** Parsed headers from row 8 */
  headers: ExcelHeader[];
  /** Data rows starting from row 10 */
  rows: ExcelRow[];
  /** Original filename */
  filename: string;
  /** Total number of rows in the file */
  totalRows: number;
  /** Number of data rows parsed */
  dataRowCount: number;
  /** Parsing metadata */
  metadata: {
    /** When the file was parsed */
    parsedAt: Date;
    /** File size in bytes */
    fileSize: number;
    /** Sheet name that was parsed */
    sheetName: string;
  };
}

export interface ExcelParseError {
  /** Error type */
  type: 'FILE_READ_ERROR' | 'INVALID_FORMAT' | 'MISSING_HEADER' | 'PARSING_ERROR';
  /** Human-readable error message */
  message: string;
  /** Technical details for debugging */
  details?: string;
  /** Row number where error occurred (if applicable) */
  rowNumber?: number;
}

export interface ExcelUploadState {
  /** Current upload/parsing status */
  status: 'idle' | 'uploading' | 'parsing' | 'success' | 'error';
  /** Parsed result if successful */
  result?: ExcelParseResult;
  /** Error information if failed */
  error?: ExcelParseError;
  /** Upload progress (0-100) */
  progress: number;
}

export interface ExcelParseOptions {
  /** Sheet index to parse (default: 0) */
  sheetIndex?: number;
  /** Sheet name to parse (overrides sheetIndex) */
  sheetName?: string;
  /** Header row number (default: 8) */
  headerRow?: number;
  /** First data row number (default: 10) */
  dataStartRow?: number;
  /** Maximum number of rows to parse (default: unlimited) */
  maxRows?: number;
  /** Whether to trim whitespace from cell values */
  trimValues?: boolean;
  /** Whether to skip empty rows */
  skipEmptyRows?: boolean;
}

/**
 * Supported Excel file types
 */
export const SUPPORTED_EXCEL_TYPES = [
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
  'application/vnd.ms-excel', // .xls
  'application/vnd.ms-excel.sheet.macroEnabled.12', // .xlsm
] as const;

/**
 * Supported Excel file extensions
 */
export const SUPPORTED_EXCEL_EXTENSIONS = ['.xlsx', '.xls', '.xlsm'] as const;

export type SupportedExcelType = typeof SUPPORTED_EXCEL_TYPES[number];
export type SupportedExcelExtension = typeof SUPPORTED_EXCEL_EXTENSIONS[number];
