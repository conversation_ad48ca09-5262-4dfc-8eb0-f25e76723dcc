import type { InvoiceValidationResult } from "./validation";

export interface FileEntry {
  name: string;
  path: string;
  type: "file" | "directory";
  size?: number;
  rawContent?: string; // For storing raw text content, e.g., for XML files
  // xmlData?: Record<string, string>[]; // Old: For storing parsed XML data as rows
  jsonData?: Record<string, unknown> | { error: string }; // New: For storing parsed XML as JSON object
  validationResult?: InvoiceValidationResult; // For storing validation results
}

// Type definitions for File System Access API
export interface FileSystemHandle {
  kind: "file" | "directory";
  name: string;
}

export interface FileSystemFileHandle extends FileSystemHandle {
  kind: "file";
  getFile(): Promise<File>;
}

export interface FileSystemDirectoryHandle extends FileSystemHandle {
  kind: "directory";
  entries(): AsyncIterableIterator<[string, FileSystemHandle]>;
  values(): AsyncIterableIterator<FileSystemHandle>;
}

// Extend DataTransferItem to include getAsFileSystemHandle
// This is necessary because the standard TypeScript lib might not include this experimental API
declare global {
  interface DataTransferItem {
    getAsFileSystemHandle?(): Promise<FileSystemHandle | null>; // Adjusted to reflect it can be null
  }
}
