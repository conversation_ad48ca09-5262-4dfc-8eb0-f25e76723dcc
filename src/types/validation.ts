// Validation types for invoice data
export interface ValidationRule {
  taxCode: string;
  name: string;
  address: string;
}

// Available condition fields for tax rate validation
// Now supports both predefined and custom fields
export type TaxRateConditionField = string;

// Predefined condition fields that map to JSON structure
export const PREDEFINED_CONDITION_FIELDS = [
  "NBan.Ten", // Seller name
  "NBan.MST", // Seller tax code
  "NBan.DChi", // Seller address
  "NMua.Ten", // Buyer name
  "NMua.MST", // Buyer tax code
  "NMua.DChi", // Buyer address
  "THHDVu", // Item name
  "DVTinh", // Unit
  "TTChung.THDon", // Invoice type
  "TTChung.KHHDon", // Invoice serial
  "TTChung.HTTToan", // Payment method
  "TTChung.DVTTe", // Currency
  "TTChung.PBan", // Version
  "TTChung.KHMSHDon", // Invoice template code
  "TTChung.SHDon", // Invoice number
  "TTChung.NLap", // Invoice date
  "TTChung.TGia", // Exchange rate
  "TTChung.MSTTCGP", // Tax authority code
] as const;

// Additional fields that can be found in the JSON structure
export const ADDITIONAL_JSON_FIELDS = [
  "TTChung.TTHDLQuan.TCHDon", // Related invoice count
  "TTChung.TTHDLQuan.LHDCLQuan", // Related invoice type
  "TTChung.TTHDLQuan.KHMSHDCLQuan", // Related invoice template
  "TTChung.TTHDLQuan.KHHDCLQuan", // Related invoice serial
  "TTChung.TTHDLQuan.SHDCLQuan", // Related invoice number
  "TTChung.TTHDLQuan.NLHDCLQuan", // Related invoice date
  "HHDVu.TChat", // Item characteristics
  "HHDVu.STT", // Item sequence number
  "HHDVu.MHHDVu", // Item code
  "HHDVu.SLuong", // Quantity
  "HHDVu.DGia", // Unit price
  "HHDVu.TLCKhau", // Discount rate
  "HHDVu.STCKhau", // Discount amount
  "HHDVu.ThTien", // Total amount
  "HHDVu.TSuat", // Tax rate
  "TToan.THTTLTSuat.LTSuat.TSuat", // Tax rate in summary
  "TToan.THTTLTSuat.LTSuat.ThTien", // Taxable amount
  "TToan.THTTLTSuat.LTSuat.TThue", // Tax amount
  "TToan.TgTCThue", // Total taxable amount
  "TToan.TgTThue", // Total tax amount
  "TToan.TTCKTMai", // Total discount
  "TToan.TgTTTBSo", // Total amount in numbers
  "TToan.TgTTTBChu", // Total amount in words
] as const;

// Tax rate condition
export interface TaxRateCondition {
  field: TaxRateConditionField;
  operator: "equals" | "contains" | "startsWith" | "endsWith";
  value: string;
  taxRate: TaxRateValue; // Each condition now has its own tax rate
}

// Available tax rates
export type TaxRateValue = 0 | 5 | 8 | 10 | "KT";

// Logic operator for combining conditions within a rule
export type LogicOperator = "AND" | "OR";

// Tax rate validation configuration types
export interface TaxRateRule {
  id: string;
  name: string;
  conditions: TaxRateCondition[];
  logicOperator: LogicOperator; // How to combine conditions: AND (all must match) or OR (any must match)
  description?: string;
}

export interface TaxRateConfig {
  rules: TaxRateRule[];
  enabled: boolean;
  lastUpdated: string;
}

// Invoice line item for tax rate validation
export interface InvoiceLineItem {
  stt?: number; // STT - Số thứ tự
  itemName?: string; // THHDVu - Tên hàng hóa dịch vụ
  itemCode?: string; // MHHDVu - Mã hàng hóa dịch vụ
  unit?: string; // DVTinh - Đơn vị tính
  quantity?: number; // SLuong - Số lượng
  unitPrice?: number; // DGia - Đơn giá
  amount?: number; // ThTien - Thành tiền
  taxRate?: number | string; // TSuat - Thuế suất (% or "KT")
  taxAmount?: number; // TThue - Tiền thuế
  characteristics?: number; // TChat - Tính chất hàng hóa dịch vụ
  discountRate?: number; // TLCKhau - Tỷ lệ chiết khấu
  discountAmount?: number; // STCKhau - Số tiền chiết khấu
}

export interface ExtractedInvoiceData {
  // Seller information (NBan)
  sellerTaxCode?: string; // NBan.MST
  sellerName?: string; // NBan.Ten
  sellerAddress?: string; // NBan.DChi
  sellerPhone?: string; // NBan.SDThoai
  sellerBankAccount?: string; // NBan.STKNHang
  sellerBankName?: string; // NBan.TNHang

  // Buyer information (NMua)
  buyerTaxCode?: string; // NMua.MST
  buyerName?: string; // NMua.Ten
  buyerAddress?: string; // NMua.DChi

  // Invoice metadata (TTChung)
  version?: string; // TTChung.PBan
  invoiceNumber?: string; // TTChung.SHDon
  invoiceDate?: string; // TTChung.NLap
  invoiceType?: string; // TTChung.THDon
  invoiceSerial?: string; // TTChung.KHHDon
  invoiceTemplate?: number; // TTChung.KHMSHDon
  currency?: string; // TTChung.DVTTe
  exchangeRate?: number; // TTChung.TGia
  paymentMethod?: string; // TTChung.HTTToan
  taxAuthorityCode?: string; // TTChung.MSTTCGP

  // Line items with tax rates (from DSHHDVu.HHDVu array)
  lineItems?: InvoiceLineItem[];

  // Total amounts (TToan)
  totalAmountBeforeTax?: number; // TToan.TTCKTMai
  totalAmount?: number; // TToan.TgTTTBSo
  totalAmountInWords?: string; // TToan.TgTTTBChu
}

export interface ValidationResult {
  field: string;
  extractedValue: string;
  expectedValue?: string;
  isValid: boolean;
  message: string;
}

// Tax rate validation result for individual line items
export interface TaxRateValidationResult {
  lineItemIndex: number;
  itemName?: string;
  extractedTaxRate?: number | string;
  isValid: boolean;
  message: string;
  appliedRule?: string;
}

export interface InvoiceValidationResult {
  fileName: string;
  extractedData: ExtractedInvoiceData;
  validationResults: ValidationResult[];
  taxRateValidationResults?: TaxRateValidationResult[];
  overallValid: boolean;
  errors: string[];
}
