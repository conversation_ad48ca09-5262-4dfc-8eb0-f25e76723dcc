import { useState, useMemo } from 'react';
import type { CustomerDocumentData } from './useCustomerDocumentExtraction';

/**
 * Custom hook for filtering customers and customer-document pairs based on separate search terms
 */
export function useCustomerDocumentFilter(
  customers: string[],
  customerDocumentPairs: CustomerDocumentData[] = []
) {
  const [documentSearchTerm, setDocumentSearchTerm] = useState('');
  const [customerSearchTerm, setCustomerSearchTerm] = useState('');

  // Filter customers based on customer search term
  const filteredCustomers = useMemo(() => {
    if (!customerSearchTerm.trim()) return customers;

    const term = customerSearchTerm.toLowerCase();
    return customers.filter(customer =>
      customer.toLowerCase().includes(term)
    );
  }, [customers, customerSearchTerm]);

  // Filter customer-document pairs based on both search terms
  const filteredCustomerDocumentPairs = useMemo(() => {
    let filtered = customerDocumentPairs;

    // Filter by document number if search term exists
    if (documentSearchTerm.trim()) {
      const docTerm = documentSearchTerm.toLowerCase();
      filtered = filtered.filter(pair =>
        pair.documentNumber.toLowerCase().includes(docTerm)
      );
    }

    // Filter by customer name if search term exists
    if (customerSearchTerm.trim()) {
      const customerTerm = customerSearchTerm.toLowerCase();
      filtered = filtered.filter(pair =>
        pair.customerName.toLowerCase().includes(customerTerm)
      );
    }

    return filtered;
  }, [customerDocumentPairs, documentSearchTerm, customerSearchTerm]);

  // Get unique customer names from filtered pairs for selection purposes
  const filteredCustomersFromPairs = useMemo(() => {
    const uniqueCustomers = new Set<string>();
    filteredCustomerDocumentPairs.forEach(pair => {
      uniqueCustomers.add(pair.customerName);
    });
    return Array.from(uniqueCustomers).sort();
  }, [filteredCustomerDocumentPairs]);

  // Clear search functions
  const clearDocumentSearch = () => setDocumentSearchTerm('');
  const clearCustomerSearch = () => setCustomerSearchTerm('');
  const clearAllSearches = () => {
    setDocumentSearchTerm('');
    setCustomerSearchTerm('');
  };

  return {
    documentSearchTerm,
    customerSearchTerm,
    setDocumentSearchTerm,
    setCustomerSearchTerm,
    clearDocumentSearch,
    clearCustomerSearch,
    clearAllSearches,
    filteredCustomers,
    filteredCustomerDocumentPairs,
    filteredCustomersFromPairs,
    hasFilter: documentSearchTerm.trim().length > 0 || customerSearchTerm.trim().length > 0,
    filteredCount: filteredCustomersFromPairs.length,
    filteredPairsCount: filteredCustomerDocumentPairs.length,
    totalCount: customers.length,
    totalPairsCount: customerDocumentPairs.length,
  };
}
