import { useState, useCallback, useMemo } from 'react';

/**
 * Custom hook for managing customer selection state and operations
 */
export function useCustomerSelection(
  customers: string[],
  onSelectionChange?: (selectedCustomers: string[]) => void
) {
  const [selectedCustomers, setSelectedCustomers] = useState<Set<string>>(new Set());

  // Convert Set to Array for external use
  const selectedCustomersList = useMemo(() => 
    Array.from(selectedCustomers), 
    [selectedCustomers]
  );

  // Handle individual customer selection
  const toggleCustomer = useCallback((customerName: string) => {
    const newSelected = new Set(selectedCustomers);
    
    if (newSelected.has(customerName)) {
      newSelected.delete(customerName);
    } else {
      newSelected.add(customerName);
    }
    
    setSelectedCustomers(newSelected);
    onSelectionChange?.(Array.from(newSelected));
  }, [selectedCustomers, onSelectionChange]);

  // Handle bulk selection for filtered customers (toggle functionality for checkbox header)
  const toggleAllFiltered = useCallback((filteredCustomers: string[]) => {
    const allFilteredSelected = filteredCustomers.length > 0 &&
      filteredCustomers.every(customer => selectedCustomers.has(customer));

    const newSelected = new Set(selectedCustomers);

    if (allFilteredSelected) {
      // Uncheck all filtered customers
      filteredCustomers.forEach(customer => newSelected.delete(customer));
    } else {
      // Check all filtered customers
      filteredCustomers.forEach(customer => newSelected.add(customer));
    }

    setSelectedCustomers(newSelected);
    onSelectionChange?.(Array.from(newSelected));
  }, [selectedCustomers, onSelectionChange]);

  // Handle selecting all filtered customers (only select, no unselect)
  const selectAllFiltered = useCallback((filteredCustomers: string[]) => {
    const newSelected = new Set(selectedCustomers);

    // Add all filtered customers to selection
    filteredCustomers.forEach(customer => newSelected.add(customer));

    setSelectedCustomers(newSelected);
    onSelectionChange?.(Array.from(newSelected));
  }, [selectedCustomers, onSelectionChange]);

  // Clear all selections
  const clearAll = useCallback(() => {
    setSelectedCustomers(new Set());
    onSelectionChange?.([]);
  }, [onSelectionChange]);

  // Check if customer is selected
  const isSelected = useCallback((customerName: string) => 
    selectedCustomers.has(customerName), 
    [selectedCustomers]
  );

  // Get selection stats for filtered customers
  const getSelectionStats = useCallback((filteredCustomers: string[]) => {
    const selectedCount = filteredCustomers.filter(customer => 
      selectedCustomers.has(customer)
    ).length;
    
    return {
      selectedCount,
      totalCount: filteredCustomers.length,
      allSelected: filteredCustomers.length > 0 && selectedCount === filteredCustomers.length,
      someSelected: selectedCount > 0 && selectedCount < filteredCustomers.length,
      noneSelected: selectedCount === 0,
    };
  }, [selectedCustomers]);

  return {
    selectedCustomers: selectedCustomersList,
    selectedCount: selectedCustomers.size,
    toggleCustomer,
    toggleAllFiltered,
    selectAllFiltered,
    clearAll,
    isSelected,
    getSelectionStats,
  };
}
