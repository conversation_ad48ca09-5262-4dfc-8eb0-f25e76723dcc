import { useState, useCallback, useRef, useEffect } from "react";
// Removed FileSystemHandle as it's not directly used in this file after refactoring
import type {
  FileEntry,
  FileSystemDirectoryHandle,
  FileSystemFileHandle,
} from "@/types/fileSystem";
import {
  getFilePaths,
  processFileList,
  revalidateFiles,
} from "@/utils/fileSystemUtils";
import { SAMPLE_VALIDATION_RULES } from "@/data/sampleValidationRules";
import { useTaxRateConfig } from "./useTaxRateConfig";

export interface UseFolderDropOptions {
  onProcessingStart?: () => void;
  onProcessingEnd?: () => void;
  onError?: (error: Error) => void;
}

export const useFolderDrop = (options?: UseFolderDropOptions) => {
  const [files, setFiles] = useState<FileEntry[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const xmlFileInputRef = useRef<HTMLInputElement>(null);

  // Ref to track the last processed tax rate config to avoid infinite loops
  const lastTaxRateConfigRef = useRef<string | null>(null);

  // Tax rate configuration hook
  const { config: taxRateConfig } = useTaxRateConfig();

  // Re-validate files when tax rate configuration changes
  useEffect(() => {
    const currentConfigString = JSON.stringify(taxRateConfig);

    // Only re-validate if tax rate config actually changed and we have files
    if (
      files.length > 0 &&
      taxRateConfig &&
      lastTaxRateConfigRef.current !== currentConfigString
    ) {
      lastTaxRateConfigRef.current = currentConfigString;

      const revalidateWithTaxRates = async () => {
        setIsProcessing(true);
        try {
          const validationRules = selectedCompany
            ? SAMPLE_VALIDATION_RULES.filter(
                (rule) => rule.taxCode === selectedCompany
              )
            : SAMPLE_VALIDATION_RULES;

          const revalidatedFiles = await revalidateFiles(
            files,
            validationRules,
            taxRateConfig
          );
          setFiles(revalidatedFiles);
        } catch (error) {
          console.error("Error re-validating files with tax rates:", error);
        } finally {
          setIsProcessing(false);
        }
      };

      revalidateWithTaxRates();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedCompany, taxRateConfig]); // Intentionally excluding 'files' to prevent infinite loop

  const processDroppedItems = useCallback(
    async (items: DataTransferItemList) => {
      const allFiles: FileEntry[] = [];
      for (const item of items) {
        if (item.kind === "file") {
          if (item.getAsFileSystemHandle) {
            // Check for File System Access API support
            const handle = await item.getAsFileSystemHandle();
            if (handle) {
              // Ensure handle is not null
              if (handle.kind === "directory") {
                const dirHandle = handle as FileSystemDirectoryHandle;
                const validationRules = selectedCompany
                  ? SAMPLE_VALIDATION_RULES.filter(
                      (rule) => rule.taxCode === selectedCompany
                    )
                  : SAMPLE_VALIDATION_RULES;
                const dirFiles = await getFilePaths(
                  dirHandle,
                  "",
                  validationRules,
                  taxRateConfig
                );
                allFiles.push(...dirFiles);
              } else {
                const fileHandle = handle as FileSystemFileHandle;
                const file = await fileHandle.getFile();
                allFiles.push({
                  name: file.name,
                  path: file.name, // For single files, path is just the name
                  type: "file",
                  size: file.size,
                });
              }
            } else {
              // Fallback for when getAsFileSystemHandle returns null or is not a file/directory
              // This might happen for non-file/directory items or if permission is denied.
              const file = item.getAsFile();
              if (file) {
                allFiles.push({
                  name: file.name,
                  path: file.name,
                  type: "file",
                  size: file.size,
                });
              }
            }
          } else {
            // Fallback for browsers not supporting getAsFileSystemHandle
            const file = item.getAsFile();
            if (file) {
              allFiles.push({
                name: file.name,
                path: file.name,
                type: "file",
                size: file.size,
              });
            }
          }
        }
      }
      return allFiles;
    },
    [selectedCompany, taxRateConfig]
  );

  const handleDrop = useCallback(
    async (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      setIsDragOver(false);
      setIsProcessing(true);
      options?.onProcessingStart?.();

      try {
        const items = event.dataTransfer.items;
        const droppedFiles = await processDroppedItems(items);
        setFiles(droppedFiles);
      } catch (error) {
        console.error("Error processing dropped files:", error);
        alert(
          "Error processing files. Please make sure you dropped a valid folder or files."
        );
        options?.onError?.(
          error instanceof Error ? error : new Error(String(error))
        );
      } finally {
        setIsProcessing(false);
        options?.onProcessingEnd?.();
      }
    },
    [processDroppedItems, options]
  );

  const handleFileSelect = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      setIsProcessing(true);
      options?.onProcessingStart?.();

      if (event.target.files) {
        try {
          const validationRules = selectedCompany
            ? SAMPLE_VALIDATION_RULES.filter(
                (rule) => rule.taxCode === selectedCompany
              )
            : SAMPLE_VALIDATION_RULES;
          const selectedFiles = await processFileList(
            event.target.files,
            validationRules,
            taxRateConfig
          );
          setFiles(selectedFiles);
        } catch (error) {
          console.error("Error processing selected files:", error);
          alert("Error processing selected files.");
          options?.onError?.(
            error instanceof Error ? error : new Error(String(error))
          );
        } finally {
          setIsProcessing(false);
          options?.onProcessingEnd?.();
        }
      }
      // Reset file input to allow selecting the same folder/files again
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [options, selectedCompany, taxRateConfig]
  );

  const handleXmlFileSelect = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      setIsProcessing(true);
      options?.onProcessingStart?.();

      if (event.target.files) {
        try {
          const validationRules = selectedCompany
            ? SAMPLE_VALIDATION_RULES.filter(
                (rule) => rule.taxCode === selectedCompany
              )
            : SAMPLE_VALIDATION_RULES;
          const selectedFiles = await processFileList(
            event.target.files,
            validationRules,
            taxRateConfig
          );
          setFiles(selectedFiles);
        } catch (error) {
          console.error("Error processing selected XML files:", error);
          alert("Error processing selected XML files.");
          options?.onError?.(
            error instanceof Error ? error : new Error(String(error))
          );
        } finally {
          setIsProcessing(false);
          options?.onProcessingEnd?.();
        }
      }
      // Reset file input to allow selecting the same files again
      if (xmlFileInputRef.current) {
        xmlFileInputRef.current.value = "";
      }
    },
    [options, selectedCompany, taxRateConfig]
  );

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      setIsDragOver(true);
    },
    []
  );

  const handleDragLeave = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();
      setIsDragOver(false);
    },
    []
  );

  const clearFiles = useCallback(() => {
    setFiles([]);
  }, []);

  const handleCompanyChange = useCallback(
    async (companyTaxCode: string | null) => {
      setSelectedCompany(companyTaxCode);

      // Re-validate existing files with the new company selection
      // Use functional update to get current files without adding to dependencies
      setFiles((currentFiles) => {
        if (currentFiles.length > 0) {
          // Trigger re-validation asynchronously
          const revalidateAsync = async () => {
            setIsProcessing(true);
            try {
              const validationRules = companyTaxCode
                ? SAMPLE_VALIDATION_RULES.filter(
                    (rule) => rule.taxCode === companyTaxCode
                  )
                : SAMPLE_VALIDATION_RULES;

              const revalidatedFiles = await revalidateFiles(
                currentFiles,
                validationRules,
                taxRateConfig
              );
              setFiles(revalidatedFiles);
            } catch (error) {
              console.error("Error re-validating files:", error);
            } finally {
              setIsProcessing(false);
            }
          };

          revalidateAsync();
        }
        return currentFiles; // Return current files unchanged for this update
      });
    },
    [taxRateConfig]
  );

  return {
    files,
    isDragOver,
    isProcessing,
    selectedCompany,
    companies: SAMPLE_VALIDATION_RULES,
    fileInputRef,
    xmlFileInputRef,
    handleDrop,
    handleFileSelect,
    handleXmlFileSelect,
    handleDragOver,
    handleDragLeave,
    clearFiles,
    handleCompanyChange,
  };
};
