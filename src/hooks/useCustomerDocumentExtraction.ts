import { useMemo } from 'react';
import type { ExcelParseResult } from '@/types/excel';

/**
 * Interface for customer-document pair data
 */
export interface CustomerDocumentData {
  customerName: string;
  documentNumber: string;
  rowNumber: number;
}

/**
 * Custom hook for extracting customer names paired with document numbers from Excel data
 */
export function useCustomerDocumentExtraction(result: ExcelParseResult) {
  // Extract customer-document pairs from Excel data
  const { customerDocumentPairs, customerColumn, documentColumn } = useMemo(() => {
    // Find customer column (column I)
    const customerColumnHeader = result.headers.find(header => 
      header.column === 'I' || 
      header.index === 8 ||
      header.name.toLowerCase().includes('tên khách hàng')
    );
    
    // Find document number column (look for "Số c/từ" or similar)
    const documentColumnHeader = result.headers.find(header => 
      header.name.toLowerCase().includes('số c/từ') ||
      header.name.toLowerCase().includes('số chứng từ') ||
      header.name.toLowerCase().includes('chứng từ') ||
      header.name.toLowerCase().includes('document') ||
      header.name.toLowerCase().includes('doc')
    );
    
    if (!customerColumnHeader) {
      console.warn('Không tìm thấy cột khách hàng. Các cột có sẵn:', 
        result.headers.map(h => `${h.column}: ${h.name}`));
      return { customerDocumentPairs: [], customerColumn: null, documentColumn: null };
    }

    if (!documentColumnHeader) {
      console.warn('Không tìm thấy cột số chứng từ. Các cột có sẵn:', 
        result.headers.map(h => `${h.column}: ${h.name}`));
      return { customerDocumentPairs: [], customerColumn: customerColumnHeader, documentColumn: null };
    }



    // Extract customer-document pairs
    const pairs: CustomerDocumentData[] = [];
    const uniquePairs = new Set<string>();
    
    result.rows.forEach(row => {
      const customerName = row.values[customerColumnHeader.name];
      const documentNumber = row.values[documentColumnHeader.name];
      
      if (customerName && typeof customerName === 'string' && 
          documentNumber && typeof documentNumber === 'string') {
        const trimmedCustomer = customerName.trim();
        const trimmedDocument = documentNumber.trim();
        
        if (trimmedCustomer && trimmedCustomer !== '' && trimmedCustomer !== '-' &&
            trimmedDocument && trimmedDocument !== '' && trimmedDocument !== '-') {
          
          // Create unique key for customer-document pair
          const pairKey = `${trimmedCustomer}|${trimmedDocument}`;
          
          if (!uniquePairs.has(pairKey)) {
            uniquePairs.add(pairKey);
            pairs.push({
              customerName: trimmedCustomer,
              documentNumber: trimmedDocument,
              rowNumber: row.rowNumber
            });
          }
        }
      }
    });

    // Sort pairs by customer name, then by document number
    pairs.sort((a, b) => {
      const customerCompare = a.customerName.localeCompare(b.customerName);
      if (customerCompare !== 0) return customerCompare;
      return a.documentNumber.localeCompare(b.documentNumber);
    });
    

    
    return { 
      customerDocumentPairs: pairs, 
      customerColumn: customerColumnHeader, 
      documentColumn: documentColumnHeader 
    };
  }, [result]);

  // Extract unique customer names for backward compatibility
  const uniqueCustomers = useMemo(() => {
    const customerNames = new Set<string>();
    customerDocumentPairs.forEach(pair => {
      customerNames.add(pair.customerName);
    });
    return Array.from(customerNames).sort();
  }, [customerDocumentPairs]);

  return {
    customerDocumentPairs,
    uniqueCustomers,
    customerColumn,
    documentColumn,
    hasCustomerData: customerDocumentPairs.length > 0,
    hasDocumentData: documentColumn !== null,
  };
}
