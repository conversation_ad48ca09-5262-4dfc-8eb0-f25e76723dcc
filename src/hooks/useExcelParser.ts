import { useState, useCallback } from 'react';
import type {
  ExcelUploadState,
  ExcelParseResult,
  ExcelParseError,
  ExcelParseOptions,
} from '@/types/excel';
import { parseExcelFile, validateExcelFile } from '@/utils/excelUtils';

/**
 * Custom hook for handling Excel file upload and parsing
 */
export function useExcelParser() {
  const [state, setState] = useState<ExcelUploadState>({
    status: 'idle',
    progress: 0,
  });

  /**
   * Resets the parser state to initial values
   */
  const reset = useCallback(() => {
    setState({
      status: 'idle',
      progress: 0,
    });
  }, []);

  /**
   * Parses an Excel file with the specified options
   */
  const parseFile = useCallback(async (
    file: File,
    options?: ExcelParseOptions
  ): Promise<ExcelParseResult | null> => {
    try {
      // Reset state and start parsing
      setState({
        status: 'uploading',
        progress: 0,
      });

      // Validate file first
      const validationError = validateExcelFile(file);
      if (validationError) {
        setState({
          status: 'error',
          error: validationError,
          progress: 0,
        });
        return null;
      }

      // Update progress
      setState(prev => ({
        ...prev,
        status: 'parsing',
        progress: 25,
      }));

      // Parse the file
      const result = await parseExcelFile(file, options);

      // Update progress
      setState(prev => ({
        ...prev,
        progress: 75,
      }));

      // Simulate final processing time
      await new Promise(resolve => setTimeout(resolve, 200));

      // Success
      setState({
        status: 'success',
        result,
        progress: 100,
      });

      return result;

    } catch (error) {
      const parseError = error as ExcelParseError;
      setState({
        status: 'error',
        error: parseError,
        progress: 0,
      });
      return null;
    }
  }, []);

  /**
   * Handles file drop or selection
   */
  const handleFileSelect = useCallback(async (
    files: FileList | File[],
    options?: ExcelParseOptions
  ): Promise<ExcelParseResult | null> => {
    const fileArray = Array.from(files);
    
    if (fileArray.length === 0) {
      setState({
        status: 'error',
        error: {
          type: 'FILE_READ_ERROR',
          message: 'Không có file nào được chọn',
        },
        progress: 0,
      });
      return null;
    }

    if (fileArray.length > 1) {
      setState({
        status: 'error',
        error: {
          type: 'FILE_READ_ERROR',
          message: 'Chỉ có thể chọn một file Excel tại một thời điểm',
        },
        progress: 0,
      });
      return null;
    }

    return parseFile(fileArray[0], options);
  }, [parseFile]);

  /**
   * Handles drag and drop events
   */
  const handleDrop = useCallback(async (
    event: React.DragEvent<HTMLElement>,
    options?: ExcelParseOptions
  ): Promise<ExcelParseResult | null> => {
    event.preventDefault();
    event.stopPropagation();

    const files = event.dataTransfer.files;
    return handleFileSelect(files, options);
  }, [handleFileSelect]);

  /**
   * Handles file input change events
   */
  const handleInputChange = useCallback(async (
    event: React.ChangeEvent<HTMLInputElement>,
    options?: ExcelParseOptions
  ): Promise<ExcelParseResult | null> => {
    const files = event.target.files;
    if (!files) return null;

    const result = await handleFileSelect(files, options);
    
    // Clear the input so the same file can be selected again
    event.target.value = '';
    
    return result;
  }, [handleFileSelect]);

  /**
   * Prevents default drag behaviors
   */
  const handleDragOver = useCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);

  const handleDragEnter = useCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLElement>) => {
    event.preventDefault();
    event.stopPropagation();
  }, []);

  return {
    // State
    state,
    isIdle: state.status === 'idle',
    isUploading: state.status === 'uploading',
    isParsing: state.status === 'parsing',
    isSuccess: state.status === 'success',
    isError: state.status === 'error',
    result: state.result,
    error: state.error,
    progress: state.progress,

    // Actions
    reset,
    parseFile,
    handleFileSelect,
    handleDrop,
    handleInputChange,
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
  };
}
