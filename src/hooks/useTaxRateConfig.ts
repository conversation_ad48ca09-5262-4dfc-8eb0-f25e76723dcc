import { useState, useEffect, useCallback } from "react";
import type {
  TaxRateConfig,
  TaxRateRule,
  TaxRateCondition,
  TaxRateValue,
} from "@/types/validation";

const STORAGE_KEY = "ttmi-tax-rate-config";

// Legacy configuration interfaces for migration
interface LegacyTaxRateCondition {
  field: string;
  operator?: string;
  value: string;
  taxRate?: TaxRateValue;
}

interface LegacyTaxRateRule {
  id: string;
  name: string;
  description?: string;
  // Old format properties
  itemNameFilter?: string;
  sellerNameFilter?: string;
  allowedRates?: TaxRateValue[];
  // New format properties
  conditions?: LegacyTaxRateCondition[];
}

interface LegacyTaxRateConfig {
  rules?: LegacyTaxRateRule[];
  enabled?: boolean;
  lastUpdated?: string;
}

// Default tax rate configuration
const DEFAULT_CONFIG: TaxRateConfig = {
  rules: [
    {
      id: "food-items",
      name: "<PERSON><PERSON><PERSON> suất cho thực phẩm",
      conditions: [
        {
          field: "THHDVu",
          operator: "contains",
          value: "đường",
          taxRate: 5,
        },
      ],
      logicOperator: "AND",
      description: "Thuế suất đặc biệt cho các mặt hàng thực phẩm",
    },
    {
      id: "plastic-items",
      name: "Thuế suất cho đồ nhựa",
      conditions: [
        {
          field: "THHDVu",
          operator: "contains",
          value: "ống hút",
          taxRate: 10,
        },
      ],
      logicOperator: "AND",
      description: "Thuế suất cho các sản phẩm nhựa như ống hút, bao bì",
    },
  ],
  enabled: true,
  lastUpdated: new Date().toISOString(),
};

export const useTaxRateConfig = () => {
  const [config, setConfig] = useState<TaxRateConfig>(DEFAULT_CONFIG);
  const [isLoading, setIsLoading] = useState(true);

  // Load configuration from localStorage on mount
  useEffect(() => {
    try {
      const savedConfig = localStorage.getItem(STORAGE_KEY);
      console.log("Raw saved config:", savedConfig);

      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig) as LegacyTaxRateConfig;
        console.log("Parsed config:", parsedConfig);

        // Migration: Convert old format to new format
        if (parsedConfig.rules) {
          const migratedRules = parsedConfig.rules
            .map((rule: LegacyTaxRateRule): TaxRateRule | null => {
              // If rule has old format (itemNameFilter/sellerNameFilter), convert to new format
              if (rule.itemNameFilter || rule.sellerNameFilter) {
                const conditions: TaxRateCondition[] = [];
                const defaultTaxRate: TaxRateValue =
                  rule.allowedRates && rule.allowedRates.length > 0
                    ? rule.allowedRates[0]
                    : 10;

                if (rule.itemNameFilter) {
                  conditions.push({
                    field: "THHDVu",
                    operator: "contains",
                    value: rule.itemNameFilter,
                    taxRate: defaultTaxRate,
                  });
                }
                if (rule.sellerNameFilter) {
                  conditions.push({
                    field: "NBan.Ten",
                    operator: "contains",
                    value: rule.sellerNameFilter,
                    taxRate: defaultTaxRate,
                  });
                }

                return {
                  id: rule.id,
                  name: rule.name,
                  conditions,
                  logicOperator: "AND", // Default to AND for migrated rules
                  description: rule.description,
                };
              }

              // Handle conditions without taxRate (add default taxRate)
              if (rule.conditions && rule.conditions.length > 0) {
                const migratedConditions: TaxRateCondition[] =
                  rule.conditions.map(
                    (condition: LegacyTaxRateCondition): TaxRateCondition => {
                      const defaultTaxRate: TaxRateValue =
                        rule.allowedRates && rule.allowedRates.length > 0
                          ? rule.allowedRates[0]
                          : 10;
                      return {
                        field: condition.field as TaxRateCondition["field"],
                        operator:
                          (condition.operator as TaxRateCondition["operator"]) ||
                          "contains",
                        value: condition.value,
                        taxRate: condition.taxRate || defaultTaxRate,
                      };
                    }
                  );

                return {
                  id: rule.id,
                  name: rule.name,
                  conditions: migratedConditions,
                  logicOperator: "AND", // Default to AND for migrated rules
                  description: rule.description,
                };
              }

              // Rule already in new format or no conditions - skip rules without conditions
              if (!rule.conditions || rule.conditions.length === 0) {
                // Skip rules that have no conditions as they are no longer supported
                return null;
              }

              // Ensure all conditions have taxRate (even for "new format" rules)
              const ensuredConditions: TaxRateCondition[] = rule.conditions.map(
                (condition: LegacyTaxRateCondition): TaxRateCondition => {
                  const defaultTaxRate: TaxRateValue =
                    rule.allowedRates && rule.allowedRates.length > 0
                      ? rule.allowedRates[0]
                      : 10;
                  return {
                    field: condition.field as TaxRateCondition["field"],
                    operator:
                      (condition.operator as TaxRateCondition["operator"]) ||
                      "contains",
                    value: condition.value,
                    taxRate: condition.taxRate || defaultTaxRate,
                  };
                }
              );

              return {
                id: rule.id,
                name: rule.name,
                conditions: ensuredConditions,
                logicOperator: "AND", // Default to AND for migrated rules
                description: rule.description,
              };
            })
            .filter((rule): rule is TaxRateRule => rule !== null); // Remove null entries with type guard

          const migratedConfig: TaxRateConfig = {
            rules: migratedRules,
            enabled: parsedConfig.enabled !== false, // Default to true
            lastUpdated: parsedConfig.lastUpdated || new Date().toISOString(),
          };

          setConfig(migratedConfig);
          // Save migrated config back to localStorage
          localStorage.setItem(STORAGE_KEY, JSON.stringify(migratedConfig));
        } else {
          // Invalid config, use default
          setConfig(DEFAULT_CONFIG);
        }
      } else {
        setConfig(DEFAULT_CONFIG);
      }
    } catch {
      // Keep default config if loading fails
      setConfig(DEFAULT_CONFIG);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save configuration to localStorage
  const saveConfig = useCallback((newConfig: TaxRateConfig) => {
    try {
      const configToSave = {
        ...newConfig,
        lastUpdated: new Date().toISOString(),
      };
      localStorage.setItem(STORAGE_KEY, JSON.stringify(configToSave));
      setConfig(configToSave);
    } catch {
      throw new Error("Unable to save configuration. Please try again.");
    }
  }, []);

  // Add a new tax rate rule
  const addRule = useCallback(
    (rule: Omit<TaxRateRule, "id">) => {
      const newRule: TaxRateRule = {
        ...rule,
        id: `rule-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      };

      const newConfig = {
        ...config,
        rules: [...config.rules, newRule],
      };

      saveConfig(newConfig);
    },
    [config, saveConfig]
  );

  // Update an existing tax rate rule
  const updateRule = useCallback(
    (ruleId: string, updates: Partial<TaxRateRule>) => {
      const newConfig = {
        ...config,
        rules: config.rules.map((rule: TaxRateRule) =>
          rule.id === ruleId ? { ...rule, ...updates } : rule
        ),
      };

      saveConfig(newConfig);
    },
    [config, saveConfig]
  );

  // Remove a tax rate rule
  const removeRule = useCallback(
    (ruleId: string) => {
      const newConfig = {
        ...config,
        rules: config.rules.filter((rule: TaxRateRule) => rule.id !== ruleId),
      };

      saveConfig(newConfig);
    },
    [config, saveConfig]
  );

  // Export configuration as JSON
  const exportConfig = useCallback(() => {
    try {
      const dataStr = JSON.stringify(config, null, 2);
      const dataBlob = new Blob([dataStr], { type: "application/json" });
      const url = URL.createObjectURL(dataBlob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `tax-rate-config-${
        new Date().toISOString().split("T")[0]
      }.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);
    } catch {
      throw new Error("Unable to export configuration. Please try again.");
    }
  }, [config]);

  // Import configuration from JSON file
  const importConfig = useCallback(
    (file: File): Promise<void> => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = (event) => {
          try {
            const content = event.target?.result as string;
            const importedConfig = JSON.parse(content) as TaxRateConfig;

            // Validate imported configuration structure
            if (!importedConfig.rules || !Array.isArray(importedConfig.rules)) {
              throw new Error("Invalid file structure: missing rules list");
            }

            // Validate each rule
            for (const rule of importedConfig.rules) {
              if (!rule.id || !rule.name || !Array.isArray(rule.conditions)) {
                throw new Error("Invalid rule structure");
              }

              // Validate conditions have required fields
              for (const condition of rule.conditions) {
                if (
                  !condition.field ||
                  !condition.value ||
                  condition.taxRate === undefined
                ) {
                  throw new Error(
                    "Invalid rule condition - missing required fields"
                  );
                }
              }
            }

            saveConfig(importedConfig);
            resolve();
          } catch (error) {
            reject(
              new Error(
                error instanceof Error
                  ? `Import error: ${error.message}`
                  : "Invalid configuration file"
              )
            );
          }
        };

        reader.onerror = () => {
          reject(new Error("Unable to read file. Please try again."));
        };

        reader.readAsText(file);
      });
    },
    [saveConfig]
  );

  // Reset to default configuration
  const resetToDefault = useCallback(() => {
    saveConfig(DEFAULT_CONFIG);
  }, [saveConfig]);

  // Force clear localStorage and reset (for debugging)
  const forceClearAndReset = useCallback(() => {
    localStorage.removeItem(STORAGE_KEY);
    setConfig(DEFAULT_CONFIG);
    localStorage.setItem(STORAGE_KEY, JSON.stringify(DEFAULT_CONFIG));
  }, []);

  return {
    config,
    isLoading,
    addRule,
    updateRule,
    removeRule,
    exportConfig,
    importConfig,
    resetToDefault,
    saveConfig,
    forceClearAndReset,
  };
};
