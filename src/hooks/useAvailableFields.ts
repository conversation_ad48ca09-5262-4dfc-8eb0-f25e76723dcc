import { useState, useCallback } from "react";
import { PREDEFINED_CONDITION_FIELDS } from "@/types/validation";

// Default field labels for predefined fields
const DEFAULT_FIELD_LABELS: Record<string, string> = {
  "NBan.Ten": "Tên người bán",
  "NBan.MST": "MST người bán",
  "NBan.DChi": "Địa chỉ người bán",
  "NMua.Ten": "Tên người mua",
  "NMua.MST": "MST người mua",
  "NMua.DChi": "Địa chỉ người mua",
  "THHDVu": "Tên hàng hóa",
  "DVTinh": "Đơn vị tính",
  "TTChung.THDon": "Loại hóa đơn",
  "TTChung.KHHDon": "Ký hiệu hóa đơn",
  "TTChung.HTTToan": "<PERSON><PERSON><PERSON> thức thanh toán",
  "TTChung.DVTTe": "Đơn vị tiền tệ",
  "TTChung.PBan": "<PERSON>ê<PERSON> bản",
  "TTChung.KHMSHDon": "Ký hiệu mẫu số hóa đơn",
  "TTChung.SHDon": "Số hóa đơn",
  "TTChung.NLap": "Ngày lập hóa đơn",
  "TTChung.TGia": "Tỷ giá",
  "TTChung.MSTTCGP": "MST cơ quan thuế cấp phát",
};

export interface FieldOption {
  value: string;
  label: string;
  isCustom?: boolean;
}

export const useAvailableFields = () => {
  // Initialize with predefined fields
  const [customFields, setCustomFields] = useState<FieldOption[]>([]);

  // Get all available fields (predefined + custom)
  const getAllAvailableFields = useCallback((): FieldOption[] => {
    const predefinedFields: FieldOption[] = PREDEFINED_CONDITION_FIELDS.map(
      (field) => ({
        value: field,
        label: DEFAULT_FIELD_LABELS[field] || field,
        isCustom: false,
      })
    );

    return [...predefinedFields, ...customFields];
  }, [customFields]);

  // Add a custom field
  const addCustomField = useCallback((field: string, label: string) => {
    setCustomFields((prev) => {
      // Check if field already exists
      const allFields = getAllAvailableFields();
      if (allFields.some((f) => f.value === field)) {
        return prev;
      }

      return [
        ...prev,
        {
          value: field,
          label: label || field,
          isCustom: true,
        },
      ];
    });
  }, [getAllAvailableFields]);

  // Remove a custom field
  const removeCustomField = useCallback((field: string) => {
    setCustomFields((prev) => prev.filter((f) => f.value !== field));
  }, []);

  // Check if a field is custom
  const isCustomField = useCallback(
    (field: string): boolean => {
      return customFields.some((f) => f.value === field);
    },
    [customFields]
  );

  // Get field label
  const getFieldLabel = useCallback(
    (field: string): string => {
      const allFields = getAllAvailableFields();
      const fieldOption = allFields.find((f) => f.value === field);
      return fieldOption?.label || field;
    },
    [getAllAvailableFields]
  );

  return {
    availableFields: getAllAvailableFields(),
    customFields,
    addCustomField,
    removeCustomField,
    isCustomField,
    getFieldLabel,
  };
};
