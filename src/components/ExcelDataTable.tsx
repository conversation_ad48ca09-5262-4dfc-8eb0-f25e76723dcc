import { useState, useMemo } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Download, 
  Search, 
  ChevronLeft, 
  ChevronRight,
  FileSpreadsheet,
  Filter,
} from 'lucide-react';
import type { ExcelParseResult } from '@/types/excel';
import { exportToExcel, formatFileSize } from '@/utils/excelUtils';

interface ExcelDataTableProps {
  result: ExcelParseResult;
  className?: string;
}

const ROWS_PER_PAGE = 50;

export default function ExcelDataTable({ result, className = '' }: ExcelDataTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  const filteredRows = useMemo(() => {
    if (!searchTerm.trim()) return result.rows;

    const term = searchTerm.toLowerCase();
    return result.rows.filter(row =>
      Object.values(row.values).some(value =>
        String(value).toLowerCase().includes(term)
      )
    );
  }, [result.rows, searchTerm]);

  // Paginate filtered rows
  const paginatedRows = useMemo(() => {
    const startIndex = (currentPage - 1) * ROWS_PER_PAGE;
    const endIndex = startIndex + ROWS_PER_PAGE;
    return filteredRows.slice(startIndex, endIndex);
  }, [filteredRows, currentPage]);

  // Calculate pagination info
  const totalPages = Math.ceil(filteredRows.length / ROWS_PER_PAGE);
  const startRow = (currentPage - 1) * ROWS_PER_PAGE + 1;
  const endRow = Math.min(currentPage * ROWS_PER_PAGE, filteredRows.length);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle export
  const handleExport = () => {
    const exportData = {
      ...result,
      rows: filteredRows, // Export filtered data
    };
    exportToExcel(exportData, `filtered_${result.filename}`);
  };

  // Handle pagination
  const handlePreviousPage = () => {
    setCurrentPage(prev => Math.max(1, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages, prev + 1));
  };

  const handlePageClick = (page: number) => {
    setCurrentPage(page);
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with file info and actions */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center gap-2">
                <FileSpreadsheet className="h-5 w-5" />
                {result.filename}
              </CardTitle>
              <div className="flex flex-wrap gap-4 mt-2 text-sm text-muted-foreground">
                <span>Kích thước: {formatFileSize(result.metadata.fileSize)}</span>
                <span>Sheet: {result.metadata.sheetName}</span>
                <span>Cột: {result.headers.length}</span>
                <span>Dòng: {result.dataRowCount}</span>
              </div>
            </div>
            
            <Button onClick={handleExport} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Xuất Excel
            </Button>
          </div>
        </CardHeader>
      </Card>

      {/* Search and filter controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Tìm kiếm trong dữ liệu..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {searchTerm && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary">
                  <Filter className="h-3 w-3 mr-1" />
                  {filteredRows.length} / {result.dataRowCount} dòng
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleSearch('')}
                >
                  Xóa bộ lọc
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Data table */}
      <Card>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            <Table>
              <TableHeader className="sticky top-0 bg-background z-10">
                <TableRow>
                  <TableHead className="w-16">#</TableHead>
                  {result.headers.map((header) => (
                    <TableHead key={header.index} className="min-w-[120px]">
                      <div className="flex flex-col">
                        <span className="font-medium">{header.name}</span>
                        <span className="text-xs text-muted-foreground font-normal">
                          Cột {header.column}
                        </span>
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedRows.length > 0 ? (
                  paginatedRows.map((row, index) => (
                    <TableRow key={row.rowNumber}>
                      <TableCell className="font-mono text-xs text-muted-foreground">
                        {(currentPage - 1) * ROWS_PER_PAGE + index + 1}
                      </TableCell>
                      {result.headers.map((header) => (
                        <TableCell key={header.index}>
                          <div className="max-w-[200px] truncate" title={String(row.values[header.name] || '')}>
                            {String(row.values[header.name] || '')}
                          </div>
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell 
                      colSpan={result.headers.length + 1} 
                      className="text-center py-8 text-muted-foreground"
                    >
                      {searchTerm ? 'Không tìm thấy dữ liệu phù hợp' : 'Không có dữ liệu'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="text-sm text-muted-foreground">
                Hiển thị {startRow}-{endRow} trong tổng số {filteredRows.length} dòng
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreviousPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Trước
                </Button>
                
                <div className="flex gap-1">
                  {getPageNumbers().map((page) => (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageClick(page)}
                      className="w-10"
                    >
                      {page}
                    </Button>
                  ))}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages}
                >
                  Sau
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
