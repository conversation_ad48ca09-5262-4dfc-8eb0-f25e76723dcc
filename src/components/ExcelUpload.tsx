import { useRef } from 'react';
import { Upload, FileSpreadsheet, AlertCircle, CheckCircle2, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useExcelParser } from '@/hooks/useExcelParser';
import { formatFileSize } from '@/utils/excelUtils';
import type { ExcelParseOptions, ExcelParseResult } from '@/types/excel';

interface ExcelUploadProps {
  /** Callback when file is successfully parsed */
  onSuccess?: (result: ExcelParseResult) => void;
  /** Callback when an error occurs */
  onError?: (error: string) => void;
  /** Custom parsing options */
  options?: ExcelParseOptions;
  /** Custom class name */
  className?: string;
}

export default function ExcelUpload({
  onSuccess,
  onError,
  options,
  className = '',
}: ExcelUploadProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const {
    state,
    isIdle,
    isUploading,
    isParsing,
    isSuccess,
    isError,
    result,
    error,
    progress,
    reset,
    handleDrop,
    handleInputChange,
    handleDragOver,
    handleDragEnter,
    handleDragLeave,
  } = useExcelParser();

  // Handle successful parsing
  const handleParseSuccess = (parseResult: ExcelParseResult) => {
    onSuccess?.(parseResult);
  };

  // Handle parsing error
  const handleParseError = (errorMessage: string) => {
    onError?.(errorMessage);
  };

  // Handle file input click
  const handleFileInputClick = () => {
    fileInputRef.current?.click();
  };

  // Handle file input change
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const result = await handleInputChange(event, options);
    if (result) {
      handleParseSuccess(result);
    } else if (error) {
      handleParseError(error.message);
    }
  };

  // Handle drop
  const handleFileDrop = async (event: React.DragEvent<HTMLDivElement>) => {
    const result = await handleDrop(event, options);
    if (result) {
      handleParseSuccess(result);
    } else if (error) {
      handleParseError(error.message);
    }
  };

  // Handle reset
  const handleReset = () => {
    reset();
  };

  return (
    <div className={`w-full max-w-2xl mx-auto ${className}`}>
      <Card>
        <CardContent className="p-6">
          {/* Upload Area */}
          {(isIdle || isError) && (
            <div
              className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center hover:border-muted-foreground/50 transition-colors cursor-pointer"
              onDrop={handleFileDrop}
              onDragOver={handleDragOver}
              onDragEnter={handleDragEnter}
              onDragLeave={handleDragLeave}
              onClick={handleFileInputClick}
            >
              <div className="flex flex-col items-center gap-4">
                <div className="p-4 bg-muted rounded-full">
                  <Upload className="h-8 w-8 text-muted-foreground" />
                </div>
                
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Tải lên file Excel</h3>
                  <p className="text-sm text-muted-foreground">
                    Kéo thả file Excel vào đây hoặc click để chọn file
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Hỗ trợ: .xlsx, .xls, .xlsm (tối đa 10MB)
                  </p>
                </div>
                
                <Button variant="outline" size="sm">
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  Chọn file Excel
                </Button>
              </div>
            </div>
          )}

          {/* Processing State */}
          {(isUploading || isParsing) && (
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
              
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">
                  {isUploading ? 'Đang tải file...' : 'Đang phân tích dữ liệu...'}
                </h3>
                <Progress value={progress} className="w-full" />
                <p className="text-sm text-muted-foreground">
                  {progress}% hoàn thành
                </p>
              </div>
            </div>
          )}

          {/* Success State */}
          {isSuccess && result && (
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-green-600">
                <CheckCircle2 className="h-5 w-5" />
                <h3 className="text-lg font-semibold">Phân tích thành công!</h3>
              </div>
              
              <div className="bg-muted/50 rounded-lg p-4 space-y-2">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Tên file:</span>
                    <p className="text-muted-foreground">{result.filename}</p>
                  </div>
                  <div>
                    <span className="font-medium">Kích thước:</span>
                    <p className="text-muted-foreground">{formatFileSize(result.metadata.fileSize)}</p>
                  </div>
                  <div>
                    <span className="font-medium">Số cột:</span>
                    <p className="text-muted-foreground">{result.headers.length}</p>
                  </div>
                  <div>
                    <span className="font-medium">Số dòng dữ liệu:</span>
                    <p className="text-muted-foreground">{result.dataRowCount}</p>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2">
                <Button onClick={handleReset} variant="outline" size="sm">
                  Tải file khác
                </Button>
              </div>
            </div>
          )}

          {/* Error State */}
          {isError && error && (
            <div className="space-y-4">
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Lỗi:</strong> {error.message}
                  {error.details && (
                    <div className="mt-2 text-xs opacity-75">
                      Chi tiết: {error.details}
                    </div>
                  )}
                </AlertDescription>
              </Alert>
              
              <Button onClick={handleReset} variant="outline" size="sm">
                Thử lại
              </Button>
            </div>
          )}

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept=".xlsx,.xls,.xlsm"
            onChange={handleFileChange}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* Parsing Instructions */}
      <Card className="mt-4">
        <CardContent className="p-4">
          <h4 className="font-medium mb-2">Quy tắc phân tích file Excel:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• <strong>Dòng 8:</strong> Tiêu đề cột (header)</li>
            <li>• <strong>Dòng 9:</strong> Bỏ qua</li>
            <li>• <strong>Dòng 10 trở đi:</strong> Dữ liệu</li>
            <li>• <strong>Cột B:</strong> Ngày chứng từ</li>
            <li>• <strong>Cột I:</strong> Tên khách hàng</li>
            <li>• <strong>Cột L:</strong> Tên vật tư</li>
            <li>• <strong>Cột M:</strong> Đvt</li>
            <li>• <strong>Cột N:</strong> Số lượng</li>
            		
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
