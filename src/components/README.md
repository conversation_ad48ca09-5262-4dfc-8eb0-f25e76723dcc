# Components

This directory contains all reusable React components for the Folder Explorer application.

## Structure

```
components/
├── ui/              # Shadcn/ui components (auto-generated)
│   ├── button.tsx
│   ├── card.tsx
│   ├── navigation-menu.tsx
│   ├── resizable.tsx
│   ├── scroll-area.tsx
│   ├── separator.tsx
│   ├── sheet.tsx
│   └── table.tsx
└── SideNav.tsx      # Custom navigation sidebar component
```

## Custom Components

### SideNav.tsx

The main navigation sidebar component that provides:

- Navigation menu with icons (Folder, File, Settings)
- Proper theming support (light/dark mode)
- Responsive design
- Integration with Shadcn/ui NavigationMenu

**Props**: None (stateless component)

**Dependencies**:
- `@/components/ui/navigation-menu`
- `@/components/ui/separator`
- `@/components/ui/scroll-area`
- `lucide-react` icons

## UI Components

The `ui/` directory contains auto-generated Shadcn/ui components. These are not meant to be edited directly. To add new UI components, use:

```bash
pnpm dlx shadcn@latest add [component-name]
```

## Usage Examples

### Using SideNav

```tsx
import SideNav from "@/components/SideNav"

export default function Layout() {
  return (
    <div className="flex">
      <SideNav />
      <main>Content</main>
    </div>
  )
}
```

### Using UI Components

```tsx
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function Example() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Example Card</CardTitle>
      </CardHeader>
      <CardContent>
        <Button>Click me</Button>
      </CardContent>
    </Card>
  )
}
```

## Styling Guidelines

- All components use Tailwind CSS for styling
- Follow the design system established by Shadcn/ui
- Use the slate color palette for consistency
- Ensure dark mode compatibility with `dark:` prefixes
- Maintain responsive design principles
