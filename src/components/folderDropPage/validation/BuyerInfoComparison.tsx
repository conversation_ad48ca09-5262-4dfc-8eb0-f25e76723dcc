import React from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  XCircle,
  FileText,
  User,
  Building,
} from "lucide-react";
import type { ExtractedInvoiceData } from "@/types/validation";
import { SAMPLE_VALIDATION_RULES } from "@/data/sampleValidationRules";

interface BuyerInfoComparisonProps {
  extractedData: ExtractedInvoiceData;
  selectedCompany?: string | null;
}

export const BuyerInfoComparison: React.FC<BuyerInfoComparisonProps> = ({
  extractedData,
  selectedCompany,
}) => {
  if (!extractedData.buyerTaxCode && !selectedCompany) {
    return null;
  }

  // Find company to check based on selectedCompany or extracted data
  const companyToCheck = selectedCompany
    ? SAMPLE_VALIDATION_RULES.find((rule) => rule.taxCode === selectedCompany)
    : SAMPLE_VALIDATION_RULES.find(
        (rule) => rule.taxCode === extractedData.buyerTaxCode
      );

  const hasMatch = extractedData.buyerTaxCode === companyToCheck?.taxCode;

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <User className="h-5 w-5" />
          Thông tin người mua cần kiểm tra
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Overall Status */}
          <div className="flex items-center justify-between p-3 rounded-lg border-2">
            <div className="flex items-center gap-2 py-2">
              <Building className="h-5 w-5" />
              <span className="font-semibold">Trạng thái kiểm tra tổng thể</span>
            </div>
            <Badge
              variant={hasMatch ? "default" : "destructive"}
              className="text-sm"
            >
              {hasMatch ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Khớp hoàn toàn
                </>
              ) : (
                <>
                  <XCircle className="h-4 w-4 mr-1" />
                  Không tìm thấy
                </>
              )}
            </Badge>
          </div>

          {/* Comparison Table */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Extracted Data */}
            <div className="space-y-3">
              <h4 className="font-semibold text-sm flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Thông tin từ hóa đơn
              </h4>
              <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="space-y-3">
                  <div>
                    <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
                      Mã số thuế
                    </span>
                    <p className="text-sm font-mono bg-white dark:bg-gray-800 p-2 rounded border mt-1">
                      {extractedData.buyerTaxCode}
                    </p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
                      Tên công ty
                    </span>
                    <p className="text-sm bg-white dark:bg-gray-800 p-2 rounded border mt-1">
                      {extractedData.buyerName || "Không có thông tin"}
                    </p>
                  </div>
                  <div>
                    <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
                      Địa chỉ
                    </span>
                    <p className="text-sm bg-white dark:bg-gray-800 p-2 rounded border mt-1">
                      {extractedData.buyerAddress || "Không có thông tin"}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Reference Data */}
            <div className="space-y-3">
              <h4 className="font-semibold text-sm flex items-center gap-2">
                <Building className="h-4 w-4" />
                Thông tin tham chiếu
              </h4>
              {companyToCheck ? (
                <div className="bg-green-50 dark:bg-green-950/30 p-4 rounded-lg border border-green-200 dark:border-green-800">
                  <div className="space-y-3">
                    <div>
                      <span className="text-xs font-medium text-green-700 dark:text-green-300">
                        Mã số thuế
                      </span>
                      <p className="text-sm font-mono bg-white dark:bg-gray-800 p-2 rounded border mt-1">
                        {companyToCheck.taxCode}
                      </p>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-green-700 dark:text-green-300">
                        Tên công ty
                      </span>
                      <p className="text-sm bg-white dark:bg-gray-800 p-2 rounded border mt-1">
                        {companyToCheck.name}
                      </p>
                    </div>
                    <div>
                      <span className="text-xs font-medium text-green-700 dark:text-green-300">
                        Địa chỉ
                      </span>
                      <p className="text-sm bg-white dark:bg-gray-800 p-2 rounded border mt-1">
                        {companyToCheck.address}
                      </p>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-red-50 dark:bg-red-950/30 p-4 rounded-lg border border-red-200 dark:border-red-800">
                  <div className="text-center py-8">
                    <XCircle className="h-12 w-12 mx-auto mb-3 text-red-500" />
                    <p className="text-sm text-red-700 dark:text-red-300 font-medium">
                      Không tìm thấy thông tin tham chiếu
                    </p>
                    <p className="text-xs text-red-600 dark:text-red-400 mt-1">
                      {selectedCompany
                        ? `Công ty "${selectedCompany}" đã được chọn để kiểm tra`
                        : `Mã số thuế "${extractedData.buyerTaxCode}" không có trong danh sách`}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
