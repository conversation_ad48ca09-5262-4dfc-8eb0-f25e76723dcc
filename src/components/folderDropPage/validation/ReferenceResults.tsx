import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, AlertCircle, Building } from "lucide-react";
import type { ExtractedInvoiceData } from "@/types/validation";
import { SAMPLE_VALIDATION_RULES } from "@/data/sampleValidationRules";
import { compareVietnameseAddresses } from "@/utils/validationUtils";

interface ReferenceResultsProps {
  extractedData: ExtractedInvoiceData;
  selectedCompany?: string | null;
}

export const ReferenceResults: React.FC<ReferenceResultsProps> = ({
  extractedData,
  selectedCompany,
}) => {
  if (!extractedData.buyerTaxCode && !selectedCompany) {
    return null;
  }

  // Find company to check based on selectedCompany or extracted data
  const companyToCheck = selectedCompany
    ? SAMPLE_VALIDATION_RULES.find((rule) => rule.taxCode === selectedCompany)
    : SAMPLE_VALIDATION_RULES.find(
        (rule) => rule.taxCode === extractedData.buyerTaxCode
      );

  // Create field comparison results
  const fieldResults = [
    {
      field: "Mã số thuế",
      extracted: extractedData.buyerTaxCode,
      expected: companyToCheck?.taxCode,
      isValid: extractedData.buyerTaxCode === companyToCheck?.taxCode,
    },
    {
      field: "Tên công ty",
      extracted: extractedData.buyerName,
      expected: companyToCheck?.name,
      isValid:
        extractedData.buyerName && companyToCheck?.name
          ? extractedData.buyerName.toLowerCase().trim() ===
            companyToCheck.name.toLowerCase().trim()
          : false,
    },
    {
      field: "Địa chỉ",
      extracted: extractedData.buyerAddress,
      expected: companyToCheck?.address,
      isValid:
        extractedData.buyerAddress && companyToCheck?.address
          ? compareVietnameseAddresses(
              extractedData.buyerAddress,
              companyToCheck.address
            )
          : false,
    },
  ].filter((result) => result.extracted); // Only show fields that have extracted data

  return (
    <Card className="border-2 border-dashed border-purple-200 dark:border-purple-800">
      <CardHeader className="pb-4 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-950/30 dark:to-violet-950/30">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Building className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          Kết quả tham chiếu từng trường
        </CardTitle>
        <p className="text-sm text-muted-foreground mt-1">
          Tổng hợp kết quả sau khi tham chiếu và so sánh từng trường thông tin
        </p>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {fieldResults.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-lg ${
                  result.isValid
                    ? "bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-950/20 dark:to-green-950/20 border-emerald-300 dark:border-emerald-700"
                    : "bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/20 dark:to-red-950/20 border-orange-300 dark:border-orange-700"
                }`}
              >
                {/* Field Header */}
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-bold text-sm text-gray-900 dark:text-gray-100">
                    {result.field}
                  </h4>
                  <div
                    className={`p-1.5 rounded-full ${
                      result.isValid
                        ? "bg-emerald-100 dark:bg-emerald-900"
                        : "bg-orange-100 dark:bg-orange-900"
                    }`}
                  >
                    {result.isValid ? (
                      <CheckCircle className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                    ) : (
                      <XCircle className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                    )}
                  </div>
                </div>

                {/* Reference Status */}
                <div
                  className={`p-3 rounded-lg mb-3 ${
                    result.isValid
                      ? "bg-emerald-100 dark:bg-emerald-900/50"
                      : "bg-orange-100 dark:bg-orange-900/50"
                  }`}
                >
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle
                      className={`h-4 w-4 ${
                        result.isValid
                          ? "text-emerald-600 dark:text-emerald-400"
                          : "text-orange-600 dark:text-orange-400"
                      }`}
                    />
                    <span
                      className={`text-xs font-semibold uppercase tracking-wide ${
                        result.isValid
                          ? "text-emerald-700 dark:text-emerald-300"
                          : "text-orange-700 dark:text-orange-300"
                      }`}
                    >
                      Kết quả tham chiếu
                    </span>
                  </div>
                  <p
                    className={`text-sm font-medium ${
                      result.isValid
                        ? "text-emerald-800 dark:text-emerald-200"
                        : "text-orange-800 dark:text-orange-200"
                    }`}
                  >
                    {result.isValid
                      ? `${result.field} khớp với dữ liệu tham chiếu`
                      : `${result.field} không khớp với dữ liệu tham chiếu`}
                  </p>
                </div>

                {/* Match Status */}
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    Trạng thái khớp:
                  </span>
                  <Badge
                    variant={result.isValid ? "default" : "destructive"}
                    className="text-xs"
                  >
                    {result.isValid ? "✓ Khớp" : "✗ Không khớp"}
                  </Badge>
                </div>

                {/* Expected vs Actual Indicator */}
                {result.expected && (
                  <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                      <div
                        className={`w-2 h-2 rounded-full ${
                          result.extracted === result.expected
                            ? "bg-emerald-500"
                            : "bg-orange-500"
                        }`}
                      ></div>
                      <span>
                        {result.extracted === result.expected
                          ? "Giá trị trùng khớp hoàn toàn"
                          : "Giá trị có sự khác biệt"}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Summary Statistics */}
          <div className="mt-6 p-4 bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/50 dark:to-slate-900/50 rounded-lg border">
            <h4 className="font-semibold text-sm mb-3 text-gray-900 dark:text-gray-100">
              Thống kê tham chiếu
            </h4>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-lg font-bold text-emerald-600 dark:text-emerald-400">
                  {fieldResults.filter((r) => r.isValid).length}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  Trường khớp
                </div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-orange-600 dark:text-orange-400">
                  {fieldResults.filter((r) => !r.isValid).length}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  Trường không khớp
                </div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                  {fieldResults.length}
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  Tổng số trường
                </div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
                  {fieldResults.length > 0
                    ? Math.round(
                        (fieldResults.filter((r) => r.isValid).length /
                          fieldResults.length) *
                          100
                      )
                    : 0}
                  %
                </div>
                <div className="text-xs text-gray-600 dark:text-gray-400">
                  Tỷ lệ khớp
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
