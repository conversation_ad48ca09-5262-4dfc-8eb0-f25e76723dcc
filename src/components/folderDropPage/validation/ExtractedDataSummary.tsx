import React from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import type { ExtractedInvoiceData } from "@/types/validation";

interface ExtractedDataSummaryProps {
  extractedData: ExtractedInvoiceData;
}

export const ExtractedDataSummary: React.FC<ExtractedDataSummaryProps> = ({
  extractedData,
}) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Thông tin trích xuất</CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        {extractedData.invoiceNumber && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
            <span className="font-medium text-sm">Số hóa đơn:</span>
            <span className="text-sm col-span-2">
              {extractedData.invoiceNumber}
            </span>
          </div>
        )}
        {extractedData.invoiceDate && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
            <span className="font-medium text-sm">Ngày lập:</span>
            <span className="text-sm col-span-2">
              {extractedData.invoiceDate}
            </span>
          </div>
        )}
        {extractedData.invoiceType && (
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2">
            <span className="font-medium text-sm">Loại hóa đơn:</span>
            <span className="text-sm col-span-2">
              {extractedData.invoiceType}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
