import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { XCircle } from "lucide-react";

interface ErrorDisplayProps {
  fileName: string;
  errors: string[];
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  fileName,
  errors,
}) => {
  return (
    <div className="p-4">
      <Card className="border-destructive">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-destructive">
            <XCircle className="h-5 w-5" />
            Lỗi xử lý file: {fileName}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {errors.map((error, index) => (
              <p
                key={index}
                className="text-sm text-destructive bg-destructive/10 p-2 rounded"
              >
                {error}
              </p>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
