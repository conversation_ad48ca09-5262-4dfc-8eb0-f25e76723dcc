import React from "react";
import { <PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, FileText } from "lucide-react";

interface ValidationHeaderProps {
  fileName: string;
  overallValid: boolean;
}

export const ValidationHeader: React.FC<ValidationHeaderProps> = ({
  fileName,
  overallValid,
}) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Kết quả kiểm tra: {fileName}
          <Badge
            variant={overallValid ? "default" : "destructive"}
            className="ml-auto"
          >
            {overallValid ? (
              <>
                <CheckCircle className="h-3 w-3 mr-1" />
                Hợp lệ
              </>
            ) : (
              <>
                <XCircle className="h-3 w-3 mr-1" />
                <PERSON><PERSON> lỗi
              </>
            )}
          </Badge>
        </CardTitle>
      </CardHeader>
    </Card>
  );
};
