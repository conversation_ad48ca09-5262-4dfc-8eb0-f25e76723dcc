import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Package,
  Building2,
} from "lucide-react";
import type {
  TaxRateValidationResult,
  ExtractedInvoiceData,
} from "@/types/validation";

interface TaxRateValidationResultsProps {
  taxRateValidationResults: TaxRateValidationResult[];
  extractedData: ExtractedInvoiceData;
}

export const TaxRateValidationResults: React.FC<
  TaxRateValidationResultsProps
> = ({ taxRateValidationResults, extractedData }) => {
  if (!taxRateValidationResults || taxRateValidationResults.length === 0) {
    return null;
  }

  const validResults = taxRateValidationResults.filter((r) => r.isValid);
  const invalidResults = taxRateValidationResults.filter((r) => !r.isValid);

  return (
    <Card className="border-2 border-dashed border-amber-200 dark:border-amber-800">
      <CardHeader className="pb-4 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-950/30 dark:to-yellow-950/30">
        <CardTitle className="flex items-center gap-2 text-lg">
          <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400" />
          Kết quả kiểm tra thuế suất
        </CardTitle>
        <div className="text-sm text-muted-foreground mt-1 space-y-1">
          <p>
            Kiểm tra thuế suất của các mặt hàng trong hóa đơn theo cấu hình đã
            thiết lập
          </p>
          {extractedData.sellerName && (
            <p>
              <span className="font-medium">Người bán:</span>{" "}
              {extractedData.sellerName}
            </p>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-4">
        <div className="space-y-4">
          {/* Summary Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-950/20 dark:to-green-950/20 rounded-lg border border-emerald-200 dark:border-emerald-700">
              <div className="text-lg font-bold text-emerald-600 dark:text-emerald-400">
                {validResults.length}
              </div>
              <div className="text-xs text-emerald-700 dark:text-emerald-300">
                Hợp lệ
              </div>
            </div>
            <div className="text-center p-3 bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 rounded-lg border border-red-200 dark:border-red-700">
              <div className="text-lg font-bold text-red-600 dark:text-red-400">
                {invalidResults.length}
              </div>
              <div className="text-xs text-red-700 dark:text-red-300">
                Không hợp lệ
              </div>
            </div>
            <div className="text-center p-3 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 rounded-lg border border-blue-200 dark:border-blue-700">
              <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                {taxRateValidationResults.length}
              </div>
              <div className="text-xs text-blue-700 dark:text-blue-300">
                Tổng số mặt hàng
              </div>
            </div>
            <div className="text-center p-3 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/20 dark:to-violet-950/20 rounded-lg border border-purple-200 dark:border-purple-700">
              <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
                {taxRateValidationResults.length > 0
                  ? Math.round(
                      (validResults.length / taxRateValidationResults.length) *
                        100
                    )
                  : 0}
                %
              </div>
              <div className="text-xs text-purple-700 dark:text-purple-300">
                Tỷ lệ hợp lệ
              </div>
            </div>
          </div>

          {/* Detailed Results */}
          <div className="space-y-3">
            <h4 className="font-semibold text-sm mb-3 text-gray-900 dark:text-gray-100">
              Chi tiết từng mặt hàng
            </h4>
            {taxRateValidationResults.map((result, index) => (
              <div
                key={index}
                className={`p-4 rounded-lg border-2 transition-all duration-200 hover:shadow-md ${
                  result.isValid
                    ? "bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-950/20 dark:to-green-950/20 border-emerald-300 dark:border-emerald-700"
                    : "bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-950/20 dark:to-orange-950/20 border-red-300 dark:border-red-700"
                }`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div
                      className={`p-2 rounded-full ${
                        result.isValid
                          ? "bg-emerald-100 dark:bg-emerald-900"
                          : "bg-red-100 dark:bg-red-900"
                      }`}
                    >
                      {result.isValid ? (
                        <CheckCircle className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                      ) : (
                        <XCircle className="h-4 w-4 text-red-600 dark:text-red-400" />
                      )}
                    </div>
                    <div>
                      <h5 className="font-semibold text-sm text-gray-900 dark:text-gray-100">
                        Mặt hàng #{result.lineItemIndex + 1}
                      </h5>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Thuế suất:{" "}
                        {result.extractedTaxRate === "KT"
                          ? "KT"
                          : `${result.extractedTaxRate}%`}
                      </p>
                    </div>
                  </div>
                  <Badge
                    variant={result.isValid ? "default" : "destructive"}
                    className="text-xs"
                  >
                    {result.isValid ? "Hợp lệ" : "Không hợp lệ"}
                  </Badge>
                </div>

                <div className="space-y-2">
                  {result.itemName && (
                    <div className="flex items-center gap-2 text-sm">
                      <Package className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Tên mặt hàng:</span>
                      <span className="text-gray-700 dark:text-gray-300">
                        {result.itemName}
                      </span>
                    </div>
                  )}

                  {extractedData.sellerName && (
                    <div className="flex items-center gap-2 text-sm">
                      <Building2 className="h-4 w-4 text-gray-500" />
                      <span className="font-medium">Người bán:</span>
                      <span className="text-gray-700 dark:text-gray-300">
                        {extractedData.sellerName}
                      </span>
                    </div>
                  )}

                  <div
                    className={`p-3 rounded-lg ${
                      result.isValid
                        ? "bg-emerald-100 dark:bg-emerald-900/50"
                        : "bg-red-100 dark:bg-red-900/50"
                    }`}
                  >
                    <div
                      className={`text-sm ${
                        result.isValid
                          ? "text-emerald-800 dark:text-emerald-200"
                          : "text-red-800 dark:text-red-200"
                      }`}
                    >
                      {result.message.split("\n").map((line, lineIndex) => {
                        // Handle empty lines for spacing
                        if (line.trim() === "") {
                          return <div key={lineIndex} className="h-2" />;
                        }

                        return (
                          <div
                            key={lineIndex}
                            className={lineIndex > 0 ? "mt-1" : ""}
                          >
                            {line}
                          </div>
                        );
                      })}
                    </div>
                    {result.appliedRule && (
                      <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                        Quy tắc áp dụng: {result.appliedRule}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
