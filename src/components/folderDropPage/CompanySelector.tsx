import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Building2 } from "lucide-react";
import type { ValidationRule } from "@/types/validation";

interface CompanySelectorProps {
  companies: ValidationRule[];
  selectedCompany: string | null;
  onCompanyChange: (companyTaxCode: string | null) => void;
  disabled?: boolean;
}

export const CompanySelector: React.FC<CompanySelectorProps> = ({
  companies,
  selectedCompany,
  onCompanyChange,
  disabled = false,
}) => {
  const handleValueChange = (value: string) => {
    if (value === "all") {
      onCompanyChange(null);
    } else {
      onCompanyChange(value);
    }
  };

  return (
    <div className="space-y-2 max-w-md">
      <div className="flex items-center gap-2 text-sm font-medium text-foreground">
        <Building2 className="h-4 w-4" />
        <span>Chọn công ty để kiểm tra:</span>
      </div>
      <Select
        value={selectedCompany || "all"}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger className="w-full bg-card border-border text-foreground hover:bg-accent/50 focus:ring-2 focus:ring-ring shadow-sm">
          <SelectValue placeholder="Chọn công ty..." />
        </SelectTrigger>
        <SelectContent className="bg-card border-border shadow-lg">
          <SelectItem
            value="all"
            className="focus:bg-accent focus:text-accent-foreground cursor-pointer px-2"
          >
            <span className="font-medium">Tất cả công ty</span>
          </SelectItem>
          {companies.map((company) => (
            <SelectItem
              key={company.taxCode}
              value={company.taxCode}
              className="focus:bg-accent focus:text-accent-foreground cursor-pointer space-y-2 p-2"
            >
              <div className="flex flex-col items-start">
                <span className="font-medium">{company.name}</span>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
};
