import { But<PERSON> } from "@/components/ui/button";
import { FolderInput, FileText, ExternalLink } from "lucide-react";
import { CompanySelector } from "./CompanySelector";
import type { ValidationRule } from "@/types/validation";

interface FolderPageHeaderProps {
  onChooseFolderClick: () => void;
  onChooseXmlFileClick: () => void;
  onClearFilesClick: () => void;
  hasFiles: boolean;
  companies: ValidationRule[];
  selectedCompany: string | null;
  onCompanyChange: (companyTaxCode: string | null) => void;
}

export const FolderPageHeader: React.FC<FolderPageHeaderProps> = ({
  onChooseFolderClick,
  onChooseXmlFileClick,
  onClearFilesClick,
  hasFiles,
  companies,
  selectedCompany,
  onCompanyChange,
}) => {
  return (
    <div className="flex flex-col gap-4 pt-4 sm:pt-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="space-y-2">
          <h1 className="text-responsive-xl font-bold text-foreground">
            KT HD
          </h1>
          <div className="space-y-1">
            <p className="text-responsive-sm text-muted-foreground">
              Kéo và thả hoặc chọn thư mục/tệp XML để xem nội dung
            </p>
            <div className="flex items-center gap-2">
              <a
                href="https://gist.github.com/quang21122/607b48839cd27a15782966747ff397dd"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-1 text-xs text-primary hover:text-primary/80 transition-colors underline underline-offset-2"
              >
                <span>📖 Hướng dẫn sử dụng</span>
                <ExternalLink className="h-3 w-3" />
              </a>
            </div>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2">
          <Button
            onClick={onChooseFolderClick}
            variant="outline"
            className="touch-target"
          >
            <FolderInput className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Chọn thư mục</span>
            <span className="sm:hidden">Thư mục</span>
          </Button>
          <Button
            onClick={onChooseXmlFileClick}
            variant="outline"
            className="touch-target"
          >
            <FileText className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Chọn tệp XML</span>
            <span className="sm:hidden">Tệp XML</span>
          </Button>
          {hasFiles && (
            <Button
              onClick={onClearFilesClick}
              variant="destructive"
              className="touch-target"
            >
              Xóa tệp
            </Button>
          )}
        </div>
      </div>

      {hasFiles && (
        <div className="w-full">
          <CompanySelector
            companies={companies}
            selectedCompany={selectedCompany}
            onCompanyChange={onCompanyChange}
            disabled={false}
          />
        </div>
      )}
    </div>
  );
};
