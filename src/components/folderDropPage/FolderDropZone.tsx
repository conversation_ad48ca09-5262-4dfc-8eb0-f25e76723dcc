import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "@/components/ui/card";
import { Upload } from "lucide-react";

interface FolderDropZoneProps {
  isDragOver: boolean;
  isProcessing: boolean;
  fileCount: number;
  onDrop: (event: React.DragEvent<HTMLDivElement>) => void;
  onDragOver: (event: React.DragEvent<HTMLDivElement>) => void;
  onDragLeave: (event: React.DragEvent<HTMLDivElement>) => void;
}

export const FolderDropZone: React.FC<FolderDropZoneProps> = ({
  isDragOver,
  isProcessing,
  fileCount,
  onDrop,
  onDragOver,
  onDragLeave,
}) => {
  return (
    <Card
      className={`h-full flex flex-col transition-all duration-300 ease-in-out transform ${
        isDragOver
          ? "border-primary bg-primary/5 scale-[1.02] shadow-lg border-2"
          : "border-dashed border-border hover:border-primary/50 hover:bg-accent/30"
      } ${isProcessing ? "animate-pulse" : ""}`}
      onDrop={onDrop}
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
    >
      <CardHeader className="text-center py-6 sm:py-8">
        <CardTitle className="flex items-center justify-center gap-2 text-responsive-lg">
          <Upload
            className={`h-6 w-6 transition-transform duration-300 ${
              isDragOver ? "scale-110" : ""
            }`}
          />
          <span className="hidden sm:inline">
            Thả thư mục hoặc tệp XML vào đây
          </span>
          <span className="sm:hidden">Thả tệp</span>
        </CardTitle>
        <CardDescription className="mt-2 text-responsive-sm">
          {isProcessing ? (
            "Đang xử lý..."
          ) : (
            <>
              <span className="hidden sm:inline">
                Kéo và thả thư mục hoặc tệp XML vào đây, hoặc sử dụng nút ở trên
              </span>
              <span className="sm:hidden">Kéo thả hoặc chọn tệp</span>
            </>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col items-center justify-center text-center py-8 sm:py-12">
        <div
          className={`text-4xl sm:text-6xl mb-4 transition-transform duration-300 ${
            isDragOver ? "scale-110" : ""
          } ${isProcessing ? "animate-bounce" : ""}`}
        >
          {isProcessing ? "⏳" : isDragOver ? "📂" : "📁"}
        </div>
        <p className="text-responsive-sm text-muted-foreground">
          {fileCount > 0
            ? `${fileCount} mục được tìm thấy`
            : "Chưa có thư mục hoặc tệp nào được tải"}
        </p>
      </CardContent>
    </Card>
  );
};
