import React from "react";
import {
  Card,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { FileEntry } from "@/types/fileSystem";
import { formatFileSize } from "@/utils/fileSystemUtils";
import { exportVietnameseInvoiceValidationToExcel } from "@/utils/excelUtils";
import { FileText, Folder, FileJson2, Download } from "lucide-react";

interface FileDisplayTableProps {
  files: FileEntry[];
  onFileSelect: (file: FileEntry) => void;
  selectedFilePath?: string | null;
  selectedCompany?: string | null;
}

export const FileDisplayTable: React.FC<FileDisplayTableProps> = ({
  files,
  onFileSelect,
  selectedFilePath,
  selectedCompany,
}) => {
  // Handle Excel export for all validation results
  const handleExportToExcel = () => {
    const validationResults = files
      .filter((file) => file.validationResult)
      .map((file) => file.validationResult!);

    if (validationResults.length === 0) {
      alert("Không có kết quả kiểm tra nào để xuất Excel.");
      return;
    }

    // Filter by selected company if specified
    const filteredResults = selectedCompany
      ? validationResults.filter(
          (result) => result.extractedData.buyerTaxCode === selectedCompany
        )
      : validationResults;

    if (filteredResults.length === 0) {
      const companyMessage = selectedCompany
        ? "công ty đã chọn"
        : "bất kỳ công ty nào";
      alert(
        `Không có kết quả kiểm tra nào cho ${companyMessage} để xuất Excel.`
      );
      return;
    }

    try {
      exportVietnameseInvoiceValidationToExcel(
        filteredResults,
        undefined,
        selectedCompany
      );
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      alert("Có lỗi xảy ra khi xuất Excel. Vui lòng thử lại.");
    }
  };

  if (files.length === 0) {
    return (
      <Card className="flex-1 min-h-0 flex flex-col">
        <CardHeader className="py-4">
          <CardTitle className="text-responsive-md">Danh sách tệp</CardTitle>
          <CardDescription className="text-responsive-sm">
            Không có tệp nào được tải
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0 flex-1 flex items-center justify-center">
          <p className="text-muted-foreground text-responsive-sm text-center px-4">
            Thả hoặc chọn tệp/thư mục để xem chúng ở đây.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="flex-1 min-h-0 flex flex-col">
      <CardHeader className="py-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle className="text-responsive-md">Danh sách tệp</CardTitle>
            <CardDescription className="text-responsive-sm">
              Đã tìm thấy {files.length} mục. Nhấp vào một tệp XML để xem chi
              tiết.
            </CardDescription>
          </div>
          <Button
            onClick={handleExportToExcel}
            variant="outline"
            size="sm"
            disabled={
              files.filter((file) => file.validationResult).length === 0
            }
          >
            <Download className="h-4 w-4 mr-2" />
            Xuất Excel
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0 flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          {/* Desktop Table View */}
          <div className="hidden sm:block">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]">Loại</TableHead>
                  <TableHead>Tên</TableHead>
                  <TableHead className="hidden lg:table-cell">
                    Đường dẫn
                  </TableHead>
                  <TableHead className="w-[100px] text-right">
                    Kích thước
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {files.map((file) => (
                  <TableRow
                    key={file.path}
                    onClick={() => onFileSelect(file)}
                    className={`cursor-pointer hover:bg-accent/50 transition-colors touch-target ${
                      selectedFilePath === file.path ? "bg-accent" : ""
                    }`}
                  >
                    <TableCell>
                      {file.type === "directory" ? (
                        <Folder className="h-4 w-4 text-blue-500" />
                      ) : file.name.toLowerCase().endsWith(".xml") ? (
                        <FileJson2 className="h-4 w-4 text-orange-500" />
                      ) : (
                        <FileText className="h-4 w-4 text-gray-500" />
                      )}
                    </TableCell>
                    <TableCell className="font-medium">{file.name}</TableCell>
                    <TableCell className="hidden lg:table-cell text-muted-foreground truncate max-w-xs">
                      {file.path}
                    </TableCell>
                    <TableCell className="text-right">
                      {formatFileSize(file.size)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Mobile Card View */}
          <div className="sm:hidden space-y-2 p-4">
            {files.map((file) => (
              <div
                key={file.path}
                onClick={() => onFileSelect(file)}
                className={`p-4 rounded-lg border cursor-pointer transition-all duration-200 touch-target ${
                  selectedFilePath === file.path
                    ? "bg-accent border-primary shadow-sm"
                    : "bg-card hover:bg-accent/30 border-border"
                }`}
              >
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0 mt-1">
                    {file.type === "directory" ? (
                      <Folder className="h-5 w-5 text-blue-500" />
                    ) : file.name.toLowerCase().endsWith(".xml") ? (
                      <FileJson2 className="h-5 w-5 text-orange-500" />
                    ) : (
                      <FileText className="h-5 w-5 text-gray-500" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-foreground truncate">
                      {file.name}
                    </h4>
                    <p className="text-sm text-muted-foreground truncate mt-1">
                      {file.path}
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatFileSize(file.size)}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};
