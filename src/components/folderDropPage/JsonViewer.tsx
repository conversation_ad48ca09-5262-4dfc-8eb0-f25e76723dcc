import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";

interface JsonViewerProps {
  jsonData: Record<string, unknown> | { error: string };
  fileName: string;
}

export const JsonViewer: React.FC<JsonViewerProps> = ({
  jsonData,
  fileName,
}) => {
  if (!jsonData) {
    return (
      <div className="p-4 text-center">
        <p className="text-responsive-sm text-muted-foreground">
          Không có dữ liệu JSON để hiển thị cho {fileName}.
        </p>
      </div>
    );
  }

  if ("error" in jsonData && typeof jsonData.error === "string") {
    return (
      <div className="p-4 text-center">
        <p className="text-responsive-sm text-destructive">
          Lỗi xử lý XML cho {fileName}: {jsonData.error}
        </p>
      </div>
    );
  }

  return (
    <ScrollArea className="h-full w-full">
      <pre className="text-xs sm:text-sm p-4 whitespace-pre-wrap break-all font-mono bg-muted/30 text-foreground leading-relaxed">
        {JSON.stringify(jsonData, null, 2)}
      </pre>
    </ScrollArea>
  );
};
