import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, Check, Info } from "lucide-react";
import { ADDITIONAL_JSON_FIELDS } from "@/types/validation";

interface CustomFieldManagerProps {
  onAddCustomField: (field: string, label: string) => void;
  existingFields: string[];
}

export const CustomFieldManager = ({
  onAddCustomField,
  existingFields,
}: CustomFieldManagerProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedField, setSelectedField] = useState<string>("");
  const [customField, setCustomField] = useState<string>("");
  const [customLabel, setCustomLabel] = useState<string>("");
  const [useCustomField, setUseCustomField] = useState(false);

  // Filter out fields that are already in use
  const availableFields = ADDITIONAL_JSON_FIELDS.filter(
    (field) => !existingFields.includes(field)
  );

  const handleAddField = () => {
    const fieldToAdd = useCustomField ? customField : selectedField;
    const labelToAdd = customLabel || fieldToAdd;

    if (!fieldToAdd.trim()) return;

    // Basic validation for custom fields
    if (useCustomField && !isValidJsonPath(fieldToAdd)) {
      alert(
        "Đường dẫn JSON không hợp lệ. Vui lòng sử dụng định dạng như: TTChung.SHDon hoặc HHDVu.TSuat"
      );
      return;
    }

    onAddCustomField(fieldToAdd, labelToAdd);

    // Reset form
    setSelectedField("");
    setCustomField("");
    setCustomLabel("");
    setUseCustomField(false);
    setIsExpanded(false);
  };

  const handleCancel = () => {
    setSelectedField("");
    setCustomField("");
    setCustomLabel("");
    setUseCustomField(false);
    setIsExpanded(false);
  };

  const isValidJsonPath = (path: string): boolean => {
    // Basic validation for JSON path format
    const pathRegex = /^[a-zA-Z][a-zA-Z0-9]*(\.[a-zA-Z][a-zA-Z0-9]*)*$/;
    return pathRegex.test(path);
  };

  const getFieldDescription = (field: string): string => {
    const descriptions: Record<string, string> = {
      "TTChung.TTHDLQuan.TCHDon": "Số lượng hóa đơn liên quan",
      "TTChung.TTHDLQuan.LHDCLQuan": "Loại hóa đơn có liên quan",
      "TTChung.TTHDLQuan.KHMSHDCLQuan": "Ký hiệu mẫu số hóa đơn có liên quan",
      "TTChung.TTHDLQuan.KHHDCLQuan": "Ký hiệu hóa đơn có liên quan",
      "TTChung.TTHDLQuan.SHDCLQuan": "Số hóa đơn có liên quan",
      "TTChung.TTHDLQuan.NLHDCLQuan": "Ngày lập hóa đơn có liên quan",
      "HHDVu.TChat": "Tính chất hàng hóa dịch vụ",
      "HHDVu.STT": "Số thứ tự hàng hóa",
      "HHDVu.MHHDVu": "Mã hàng hóa dịch vụ",
      "HHDVu.SLuong": "Số lượng",
      "HHDVu.DGia": "Đơn giá",
      "HHDVu.TLCKhau": "Tỷ lệ chiết khấu",
      "HHDVu.STCKhau": "Số tiền chiết khấu",
      "HHDVu.ThTien": "Thành tiền",
      "HHDVu.TSuat": "Thuế suất của hàng hóa",
      "TToan.THTTLTSuat.LTSuat.TSuat": "Thuế suất trong tổng hợp",
      "TToan.THTTLTSuat.LTSuat.ThTien": "Thành tiền theo thuế suất",
      "TToan.THTTLTSuat.LTSuat.TThue": "Tiền thuế theo thuế suất",
      "TToan.TgTCThue": "Tổng tiền chưa có thuế",
      "TToan.TgTThue": "Tổng tiền thuế",
      "TToan.TTCKTMai": "Tổng tiền chiết khấu thương mại",
      "TToan.TgTTTBSo": "Tổng tiền thanh toán bằng số",
      "TToan.TgTTTBChu": "Tổng tiền thanh toán bằng chữ",
    };
    return descriptions[field] || "Trường tùy chỉnh";
  };

  if (!isExpanded) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsExpanded(true)}
        className="flex items-center gap-2 text-slate-500 border-slate-200 bg-white hover:bg-slate-50 hover:border-slate-300 hover:text-slate-500 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
      >
        <Plus className="h-4 w-4" />
        Thêm trường tùy chỉnh
      </Button>
    );
  }

  return (
    <div className="space-y-4 p-4 bg-gradient-to-br from-slate-50 to-slate-100/50 border border-slate-200 rounded-lg shadow-sm backdrop-blur-sm">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
          <span className="text-sm font-semibold text-slate-500">
            Thêm trường điều kiện mới
          </span>
        </div>
      </div>

      {/* Field Selection Mode */}
      <div className="space-y-3">
        <div className="flex gap-2">
          <label
            className={`flex items-center space-x-3 cursor-pointer group px-4 py-3 rounded-lg border-2 transition-all duration-200 ${
              !useCustomField
                ? "bg-emerald-100 border-emerald-300 shadow-md ring-2 ring-emerald-200/50"
                : "bg-white border-slate-200 hover:border-slate-300 hover:bg-slate-50"
            }`}
          >
            <div className="relative">
              <input
                type="radio"
                checked={!useCustomField}
                onChange={() => setUseCustomField(false)}
                className="sr-only"
              />
              <div
                className={`w-4 h-4 rounded-full border-2 transition-all duration-200 ${
                  !useCustomField
                    ? "border-emerald-400 bg-emerald-400"
                    : "border-slate-300 bg-white group-hover:border-slate-400"
                }`}
              >
                {!useCustomField && (
                  <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                )}
              </div>
            </div>
            <span
              className={`text-sm font-semibold transition-colors duration-200 ${
                !useCustomField
                  ? "text-emerald-700"
                  : "text-slate-500 group-hover:text-slate-600"
              }`}
            >
              Chọn từ danh sách
            </span>
          </label>
          <label
            className={`flex items-center space-x-3 cursor-pointer group px-4 py-3 rounded-lg border-2 transition-all duration-200 ${
              useCustomField
                ? "bg-emerald-100 border-emerald-300 shadow-md ring-2 ring-emerald-200/50"
                : "bg-white border-slate-200 hover:border-slate-300 hover:bg-slate-50"
            }`}
          >
            <div className="relative">
              <input
                type="radio"
                checked={useCustomField}
                onChange={() => setUseCustomField(true)}
                className="sr-only"
              />
              <div
                className={`w-4 h-4 rounded-full border-2 transition-all duration-200 ${
                  useCustomField
                    ? "border-emerald-400 bg-emerald-400"
                    : "border-slate-300 bg-white group-hover:border-slate-400"
                }`}
              >
                {useCustomField && (
                  <div className="w-2 h-2 bg-white rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></div>
                )}
              </div>
            </div>
            <span
              className={`text-sm font-semibold transition-colors duration-200 ${
                useCustomField
                  ? "text-emerald-700"
                  : "text-slate-500 group-hover:text-slate-600"
              }`}
            >
              Tạo trường tùy chỉnh
            </span>
          </label>
        </div>
      </div>

      {/* Predefined Field Selection */}
      {!useCustomField && (
        <div className="space-y-2">
          <Select value={selectedField} onValueChange={setSelectedField}>
            <SelectTrigger className="h-10 text-sm !bg-white border-slate-200 hover:border-slate-300 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 transition-all duration-200 shadow-sm [&>span]:!text-slate-500">
              <SelectValue
                placeholder="Chọn trường từ cấu trúc JSON..."
                className="text-slate-500"
              />
            </SelectTrigger>
            <SelectContent
              className="!bg-white border-slate-200 shadow-xl rounded-lg z-50"
              position="popper"
              side="bottom"
              align="start"
              sideOffset={8}
              alignOffset={0}
              style={{
                width: "min(400px, 90vw)",
                minWidth: "320px",
                maxHeight: "300px",
              }}
            >
              <div
                className="overflow-y-auto scrollbar-thin scrollbar-thumb-slate-300 scrollbar-track-slate-100"
                style={{ maxHeight: "280px" }}
              >
                {availableFields.map((field) => (
                  <SelectItem
                    key={field}
                    value={field}
                    className="py-3 px-4 text-slate-500 hover:bg-emerald-50 focus:bg-emerald-50 cursor-pointer text-sm transition-colors duration-150 border-b border-slate-100 last:border-b-0"
                  >
                    <div className="flex flex-col w-full">
                      <span
                        className="font-semibold truncate text-slate-500"
                        title={field}
                        style={{ maxWidth: "350px" }}
                      >
                        {field}
                      </span>
                      <span
                        className="text-xs text-slate-400 truncate mt-1"
                        title={getFieldDescription(field)}
                        style={{ maxWidth: "350px" }}
                      >
                        {getFieldDescription(field)}
                      </span>
                    </div>
                  </SelectItem>
                ))}
                {availableFields.length === 0 && (
                  <div className="py-4 px-4 text-center">
                    <div className="flex items-center justify-center gap-2 text-amber-600">
                      <Info className="h-4 w-4" />
                      <span className="text-sm font-medium">
                        Tất cả các trường có sẵn đã được sử dụng
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Custom Field Input */}
      {useCustomField && (
        <div className="space-y-3">
          <Input
            value={customField}
            onChange={(e) => setCustomField(e.target.value)}
            placeholder="Ví dụ: TTChung.SHDon hoặc HHDVu.TSuat"
            className="h-10 text-sm !bg-white border-slate-200 hover:border-slate-300 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 !text-slate-500 placeholder:!text-slate-400 transition-all duration-200 shadow-sm"
          />
          <div className="flex items-start gap-3 p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
            <Info className="h-4 w-4 text-emerald-600 mt-0.5 shrink-0" />
            <div className="text-sm text-emerald-600">
              <span className="font-semibold">Hướng dẫn: </span>
              <span>Sử dụng dấu chấm (.) để phân tách các cấp trong JSON</span>
              <div className="mt-1 text-xs text-emerald-600">
                Ví dụ:{" "}
                <code className="bg-emerald-100 px-1 py-0.5 rounded">
                  TTChung.SHDon
                </code>{" "}
                hoặc{" "}
                <code className="bg-emerald-100 px-1 py-0.5 rounded">
                  HHDVu.TSuat
                </code>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Custom Label */}
      <div className="space-y-2">
        <Input
          value={customLabel}
          onChange={(e) => setCustomLabel(e.target.value)}
          placeholder="Nhãn hiển thị (tùy chọn)..."
          className="h-10 text-sm !bg-white border-slate-200 hover:border-slate-300 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-400/20 !text-slate-500 placeholder:!text-slate-400 transition-all duration-200 shadow-sm"
        />
        <p className="text-xs text-slate-500">
          Nếu để trống, sẽ sử dụng đường dẫn JSON làm nhãn hiển thị
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 pt-2 border-t border-slate-200">
        <Button
          variant="outline"
          size="sm"
          onClick={handleCancel}
          className="flex-1 h-9 px-4 text-sm text-slate-500 border-slate-200 bg-white hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 font-medium"
        >
          Hủy
        </Button>
        <Button
          size="sm"
          onClick={handleAddField}
          disabled={!useCustomField ? !selectedField : !customField.trim()}
          className="flex-1 h-9 px-4 text-sm bg-emerald-400 text-white hover:bg-emerald-500 disabled:bg-slate-200 disabled:text-slate-400 transition-all duration-200 font-semibold shadow-sm hover:shadow-md"
        >
          <Check className="h-4 w-4 mr-2" />
          Thêm trường
        </Button>
      </div>
    </div>
  );
};
