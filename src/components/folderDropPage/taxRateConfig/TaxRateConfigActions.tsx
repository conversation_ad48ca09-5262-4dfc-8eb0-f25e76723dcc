import { useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Download, Upload, RotateCcw } from "lucide-react";

interface TaxRateConfigActionsProps {
  onExport: () => void;
  onImport: (file: File) => Promise<void>;
  onReset: () => void;
  importError: string | null;
  onImportErrorChange: (error: string | null) => void;
}

export const TaxRateConfigActions = ({
  onExport,
  onImport,
  onReset,
  importError,
  onImportErrorChange,
}: TaxRateConfigActionsProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    onImportErrorChange(null);
    try {
      await onImport(file);
    } catch (error) {
      onImportErrorChange(
        error instanceof Error ? error.message : "Lỗi không xác định"
      );
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const handleExport = () => {
    try {
      onExport();
    } catch (error) {
      console.error("Export error:", error);
    }
  };

  return (
    <>
      {/* Configuration Actions */}
      <div className="flex flex-wrap gap-3">
        <Button
          variant="outline"
          size="sm"
          onClick={handleExport}
          className="flex items-center gap-2 text-foreground border-border hover:bg-muted hover:border-muted-foreground/50 transition-all duration-200"
        >
          <Download className="h-4 w-4" />
          Tải xuống cấu hình
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => fileInputRef.current?.click()}
          className="flex items-center gap-2 text-foreground border-border hover:bg-muted hover:border-muted-foreground/50 transition-all duration-200"
        >
          <Upload className="h-4 w-4" />
          Tải lên cấu hình
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onReset}
          className="flex items-center gap-2 text-foreground border-border hover:bg-muted hover:border-muted-foreground/50 transition-all duration-200"
        >
          <RotateCcw className="h-4 w-4" />
          Đặt lại mặc định
        </Button>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".json"
        onChange={handleFileImport}
        className="hidden"
      />

      {/* Import error display */}
      {importError && (
        <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
          <p className="text-sm text-destructive">{importError}</p>
        </div>
      )}
    </>
  );
};
