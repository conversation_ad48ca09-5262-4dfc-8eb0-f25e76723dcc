import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { X, Info } from "lucide-react";
import type { TaxRateConditionField, TaxRateValue } from "@/types/validation";
// import { PREDEFINED_CONDITION_FIELDS } from "@/types/validation";
import { CustomFieldManager } from "./CustomFieldManager";

interface ConditionRowProps {
  field: TaxRateConditionField;
  value: string;
  taxRate: TaxRateValue;
  onFieldChange: (field: TaxRateConditionField) => void;
  onValueChange: (value: string) => void;
  onTaxRateChange: (taxRate: TaxRateValue) => void;
  onRemove: () => void;
  showRemove?: boolean;
  availableFields: Array<{ value: string; label: string }>;
  onAddCustomField?: (field: string, label: string) => void;
}

// Available tax rates
const TAX_RATES: { value: TaxRateValue; label: string }[] = [
  { value: 0, label: "0" },
  { value: 5, label: "5" },
  { value: 8, label: "8" },
  { value: 10, label: "10" },
  { value: "KT", label: "KT" },
];

export const ConditionRow = ({
  field,
  value,
  taxRate,
  onFieldChange,
  onValueChange,
  onTaxRateChange,
  onRemove,
  showRemove = true,
  availableFields,
  onAddCustomField,
}: ConditionRowProps) => {
  return (
    <div className="flex flex-col lg:flex-row items-stretch lg:items-center gap-4 p-5 bg-background border border-border rounded-lg transition-all duration-200 hover:border-muted-foreground/30 hover:shadow-sm">
      {/* Condition Field Section */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-3 flex-1">
        <div className="text-sm font-medium text-foreground min-w-[80px] shrink-0">
          Điều kiện
        </div>
        <div className="min-w-0 flex-1 sm:max-w-[240px]">
          <Select value={field} onValueChange={onFieldChange}>
            <SelectTrigger className="h-10 bg-background border-border hover:border-muted-foreground/50 focus:border-foreground focus:ring-1 focus:ring-muted-foreground/20 transition-all duration-200">
              <SelectValue className="text-foreground" />
            </SelectTrigger>
            <SelectContent
              className="bg-background border-border shadow-lg p-0"
              position="popper"
              sideOffset={4}
              style={{
                width: "min(450px, 95vw)",
                minWidth: "320px",
                maxHeight: "70vh",
              }}
            >
              <div className="flex flex-col max-h-full">
                {/* Scrollable items section */}
                <div
                  className="overflow-y-auto flex-shrink-1"
                  style={{
                    maxHeight: "200px",
                    minHeight: "120px",
                  }}
                >
                  {availableFields.map((fieldOption) => (
                    <SelectItem
                      key={fieldOption.value}
                      value={fieldOption.value}
                      className="py-2.5 px-3 text-foreground hover:bg-muted focus:bg-muted cursor-pointer transition-colors text-sm"
                    >
                      <div
                        className="truncate"
                        title={fieldOption.label}
                        style={{ maxWidth: "380px" }}
                      >
                        {fieldOption.label}
                      </div>
                    </SelectItem>
                  ))}
                </div>

                {/* Fixed custom field manager at bottom - no scroll */}
                {onAddCustomField && (
                  <div className="border-t border-border bg-muted/30 flex-shrink-0 overflow-visible">
                    <div className="p-2">
                      <CustomFieldManager
                        onAddCustomField={onAddCustomField}
                        existingFields={availableFields.map((f) => f.value)}
                      />
                    </div>
                  </div>
                )}
              </div>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Input Field Section */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-3 flex-1">
        <div className="text-sm font-medium text-foreground lg:hidden">
          Giá trị
        </div>
        <div className="min-w-0 flex-1 sm:max-w-[200px]">
          <div className="space-y-2">
            <Input
              value={value}
              onChange={(e) => onValueChange(e.target.value)}
              placeholder="Nhập giá trị hoặc pattern..."
              className="h-10 bg-background border-border hover:border-muted-foreground/50 focus:border-foreground focus:ring-1 focus:ring-muted-foreground/20 text-left font-medium text-foreground placeholder:text-muted-foreground transition-all duration-200"
            />
            {value.includes("*") && (
              <div className="flex items-start gap-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                <Info className="h-4 w-4 text-blue-600 mt-0.5 shrink-0" />
                <div className="text-xs text-blue-700">
                  <div className="font-medium mb-1">
                    Pattern wildcard được phát hiện:
                  </div>
                  <div className="space-y-1">
                    {value.startsWith("*") && value.endsWith("*") && (
                      <div>
                        •{" "}
                        <code className="bg-blue-100 px-1 rounded">
                          *{value.slice(1, -1)}*
                        </code>{" "}
                        = chứa "{value.slice(1, -1)}"
                      </div>
                    )}
                    {value.startsWith("*") && !value.endsWith("*") && (
                      <div>
                        •{" "}
                        <code className="bg-blue-100 px-1 rounded">
                          *{value.slice(1)}
                        </code>{" "}
                        = kết thúc bằng "{value.slice(1)}"
                      </div>
                    )}
                    {!value.startsWith("*") && value.endsWith("*") && (
                      <div>
                        •{" "}
                        <code className="bg-blue-100 px-1 rounded">
                          {value.slice(0, -1)}*
                        </code>{" "}
                        = bắt đầu bằng "{value.slice(0, -1)}"
                      </div>
                    )}
                    {value === "*" && (
                      <div>
                        • <code className="bg-blue-100 px-1 rounded">*</code> =
                        khớp với bất kỳ giá trị nào
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Tax Rate Section */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-3 flex-1">
        <div className="text-sm font-medium text-foreground min-w-[70px] shrink-0">
          Thuế suất
        </div>
        <div className="min-w-0 flex-1 sm:max-w-[100px]">
          <Select
            value={taxRate.toString()}
            onValueChange={(value) => {
              const numValue = Number(value);
              onTaxRateChange(
                isNaN(numValue)
                  ? (value as TaxRateValue)
                  : (numValue as TaxRateValue)
              );
            }}
          >
            <SelectTrigger className="h-10 bg-background border-border hover:border-muted-foreground/50 focus:border-foreground focus:ring-1 focus:ring-muted-foreground/20 transition-all duration-200">
              <SelectValue className="text-foreground" />
            </SelectTrigger>
            <SelectContent
              className="bg-background border-border shadow-lg"
              position="popper"
              sideOffset={4}
              style={{
                width: "min(200px, 50vw)",
                minWidth: "120px",
                maxHeight: "240px",
              }}
            >
              <div className="overflow-y-auto" style={{ maxHeight: "200px" }}>
                {TAX_RATES.map((rate) => (
                  <SelectItem
                    key={rate.value}
                    value={rate.value.toString()}
                    className="py-2.5 px-3 text-foreground hover:bg-muted focus:bg-muted cursor-pointer transition-colors"
                  >
                    <span className="font-medium">{rate.label}</span>
                  </SelectItem>
                ))}
              </div>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Remove Button */}
      {showRemove && (
        <div className="flex justify-end lg:justify-center">
          <Button
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="h-10 w-10 p-0 text-muted-foreground hover:text-destructive hover:bg-muted transition-all duration-200 shrink-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
};
