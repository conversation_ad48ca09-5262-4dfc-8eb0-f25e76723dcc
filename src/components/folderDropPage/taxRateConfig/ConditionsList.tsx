import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ConditionRow } from "./ConditionRow";
import type {
  TaxRateConditionField,
  TaxRateValue,
  LogicOperator,
} from "@/types/validation";

interface ConditionsListProps {
  conditions: Array<{
    field: TaxRateConditionField;
    value: string;
    taxRate: TaxRateValue;
  }>;
  logicOperator: LogicOperator;
  onConditionChange: (
    index: number,
    field: TaxRateConditionField,
    value: string,
    taxRate: TaxRateValue
  ) => void;
  onRemoveCondition: (index: number) => void;
  onAddCondition: () => void;
  availableFields: Array<{ value: string; label: string }>;
  onAddCustomField?: (field: string, label: string) => void;
}

export const ConditionsList = ({
  conditions,
  logicOperator,
  onConditionChange,
  onRemoveCondition,
  onAddCondition,
  availableFields,
  onAddCustomField,
}: ConditionsListProps) => {
  return (
    <div className="space-y-4">
      {/* Conditions */}
      {conditions.map((condition, index) => (
        <div key={index} className="space-y-4">
          <ConditionRow
            field={condition.field}
            value={condition.value}
            taxRate={condition.taxRate}
            onFieldChange={(field) =>
              onConditionChange(
                index,
                field,
                condition.value,
                condition.taxRate
              )
            }
            onValueChange={(value) =>
              onConditionChange(
                index,
                condition.field,
                value,
                condition.taxRate
              )
            }
            onTaxRateChange={(taxRate) =>
              onConditionChange(
                index,
                condition.field,
                condition.value,
                taxRate
              )
            }
            onRemove={() => onRemoveCondition(index)}
            showRemove={conditions.length > 1}
            availableFields={availableFields}
            onAddCustomField={onAddCustomField}
          />

          {/* Logic connector - show between conditions but not after the last one */}
          {index < conditions.length - 1 && (
            <div className="flex justify-center py-3">
              <div className="relative w-full max-w-sm">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-border"></div>
                </div>
                <div className="relative flex justify-center">
                  <span className="bg-background px-4 py-1.5 text-sm font-medium text-muted-foreground border border-border rounded-md">
                    {logicOperator === "AND" ? "và" : "hoặc"}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      ))}

      {/* Add Condition Button */}
      <div className="flex justify-center pt-2">
        <Button
          variant="outline"
          onClick={onAddCondition}
          className="flex items-center gap-2 text-foreground border-border hover:bg-muted hover:border-muted-foreground/50 px-4 py-2 h-auto font-medium transition-all duration-200"
        >
          <Plus className="h-4 w-4" />
          Thêm điều kiện
        </Button>
      </div>
    </div>
  );
};
