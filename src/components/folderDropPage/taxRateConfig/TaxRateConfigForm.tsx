import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Save, Plus, Edit, X } from "lucide-react";
import { ConditionsList } from "./ConditionsList";
import { useAvailableFields } from "@/hooks/useAvailableFields";
import type {
  TaxRateRule,
  TaxRateConditionField,
  TaxRateValue,
  LogicOperator,
} from "@/types/validation";

interface TaxRateConfigFormProps {
  onAddRule: (rule: Omit<TaxRateRule, "id">) => void;
  onUpdateRule: (ruleId: string, rule: Omit<TaxRateRule, "id">) => void;
  existingRules: TaxRateRule[];
  onRemoveRule: (id: string) => void;
}

interface SimpleCondition {
  field: TaxRateConditionField;
  value: string;
  taxRate: TaxRateValue;
}

export const TaxRateConfigForm = ({
  onAddRule,
  onUpdateRule,
  existingRules,
  onRemoveRule,
}: TaxRateConfigFormProps) => {
  const { availableFields, addCustomField } = useAvailableFields();
  const [isAddingRule, setIsAddingRule] = useState(false);
  const [editingRuleId, setEditingRuleId] = useState<string | null>(null);
  const [ruleName, setRuleName] = useState("");
  const [ruleDescription, setRuleDescription] = useState("");
  const [logicOperator, setLogicOperator] = useState<LogicOperator>("AND");
  const [conditions, setConditions] = useState<SimpleCondition[]>([
    {
      field: "NBan.Ten",
      value: "",
      taxRate: 10,
    },
  ]);

  const isEditMode = editingRuleId !== null;

  const handleConditionChange = (
    index: number,
    field: TaxRateConditionField,
    value: string,
    taxRate: TaxRateValue
  ) => {
    setConditions((prev) =>
      prev.map((condition, i) =>
        i === index ? { field, value, taxRate } : condition
      )
    );
  };

  const handleRemoveCondition = (index: number) => {
    setConditions((prev) => prev.filter((_, i) => i !== index));
  };

  const handleAddCondition = () => {
    setConditions((prev) => [
      ...prev,
      {
        field: "NBan.Ten",
        value: "",
        taxRate: 10,
      },
    ]);
  };

  const handleEditRule = (rule: TaxRateRule) => {
    // Populate form with rule data
    setRuleName(rule.name);
    setRuleDescription(rule.description || "");
    setLogicOperator(rule.logicOperator);

    // Convert rule conditions to simple conditions format
    const simpleConditions: SimpleCondition[] = rule.conditions.map(
      (condition) => ({
        field: condition.field,
        value: condition.value,
        taxRate: condition.taxRate,
      })
    );

    setConditions(
      simpleConditions.length > 0
        ? simpleConditions
        : [
            {
              field: "NBan.Ten",
              value: "",
              taxRate: 10,
            },
          ]
    );

    setEditingRuleId(rule.id);
    setIsAddingRule(true); // Show the form
  };

  const handleSaveRule = () => {
    if (!ruleName.trim()) return;

    // Convert simple conditions to the new format with tax rates
    const ruleConditions = conditions
      .filter((c) => c.value.trim())
      .map((c) => ({
        field: c.field,
        operator: "contains" as const,
        value: c.value,
        taxRate: c.taxRate, // Include tax rate in each condition
      }));

    const ruleData: Omit<TaxRateRule, "id"> = {
      name: ruleName,
      description: ruleDescription,
      conditions: ruleConditions,
      logicOperator: logicOperator,
    };

    if (isEditMode && editingRuleId) {
      // Update existing rule
      onUpdateRule(editingRuleId, ruleData);
    } else {
      // Add new rule
      onAddRule(ruleData);
    }

    // Reset form
    handleCancel();
  };

  const handleCancel = () => {
    setRuleName("");
    setRuleDescription("");
    setLogicOperator("AND");
    setConditions([
      {
        field: "NBan.Ten",
        value: "",
        taxRate: 10,
      },
    ]);
    setIsAddingRule(false);
    setEditingRuleId(null);
  };

  if (!isAddingRule) {
    return (
      <div className="space-y-4">
        {/* Existing Rules Display */}
        {existingRules.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-semibold text-base text-foreground">
              Quy tắc hiện tại
            </h4>
            {existingRules.map((rule) => (
              <Card
                key={rule.id}
                className="p-4 bg-muted/30 border border-border shadow-sm hover:shadow-md hover:border-muted-foreground/30 transition-all duration-200"
              >
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <h5 className="font-medium text-sm text-foreground truncate">
                      {rule.name}
                    </h5>
                    {rule.description && (
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                        {rule.description}
                      </p>
                    )}
                    <div className="text-xs text-muted-foreground mt-2 flex flex-col gap-y-1">
                      <span>
                        Điều kiện (
                        {rule.logicOperator === "AND"
                          ? "Tất cả phải đúng"
                          : "Chỉ cần một điều kiện đúng"}
                        ):
                      </span>
                      {rule.conditions.length === 0 && (
                        <span className="text-muted-foreground/50">
                          Không có điều kiện
                        </span>
                      )}
                      {rule.conditions.map((condition, index) => (
                        <span key={index} className="flex space-x-2">
                          <span>
                            {index + 1}. {condition.field}: {condition.value}
                          </span>
                          <span className="font-medium">
                            →{" "}
                            {condition.taxRate === "KT"
                              ? "KT"
                              : `${condition.taxRate}%`}
                          </span>
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditRule(rule)}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <Edit className="h-4 w-4 mr-1" />
                      Sửa
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onRemoveRule(rule.id)}
                      className="text-destructive hover:text-destructive"
                    >
                      Xóa
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        )}

        {/* Add New Rule Button */}
        <Button
          variant="outline"
          onClick={() => setIsAddingRule(true)}
          className="w-full flex items-center gap-2 border-dashed border-2 border-border h-12 text-foreground hover:bg-muted hover:border-muted-foreground/50 font-medium transition-all duration-200"
        >
          <Plus className="h-4 w-4" />
          Thêm quy tắc mới
        </Button>
      </div>
    );
  }

  return (
    <Card className="border border-border shadow-lg bg-card">
      <CardContent className="p-6 space-y-6">
        {/* Form Header */}
        {isEditMode && (
          <div className="flex items-center gap-2 pb-4 border-b border-border">
            <Edit className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold text-foreground">
              Chỉnh sửa quy tắc
            </h3>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCancel}
              className="ml-auto text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4 mr-1" />
              Hủy chỉnh sửa
            </Button>
          </div>
        )}

        {/* Rule Name */}
        <div className="space-y-2">
          <Label
            htmlFor="rule-name"
            className="text-sm font-medium text-foreground"
          >
            Tên quy tắc *
          </Label>
          <Input
            id="rule-name"
            value={ruleName}
            onChange={(e) => setRuleName(e.target.value)}
            placeholder="Nhập tên quy tắc..."
            className="h-10 bg-background border-border hover:border-muted-foreground/50 focus:border-foreground focus:ring-1 focus:ring-muted-foreground/20 transition-all duration-200"
          />
        </div>

        {/* Rule Description */}
        <div className="space-y-2">
          <Label
            htmlFor="rule-description"
            className="text-sm font-medium text-foreground"
          >
            Mô tả (tùy chọn)
          </Label>
          <Textarea
            id="rule-description"
            value={ruleDescription}
            onChange={(e) => setRuleDescription(e.target.value)}
            placeholder="Mô tả về quy tắc này..."
            className="bg-background border-border hover:border-muted-foreground/50 focus:border-foreground focus:ring-1 focus:ring-muted-foreground/20 transition-all duration-200 resize-none"
            rows={3}
          />
        </div>

        {/* Logic Operator Selection */}
        <div className="space-y-3">
          <Label className="text-sm font-medium text-foreground">
            Cách kết hợp các điều kiện
          </Label>
          <RadioGroup
            value={logicOperator}
            onValueChange={(value: string) =>
              setLogicOperator(value as LogicOperator)
            }
            className="flex flex-row gap-6"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="AND" id="and" />
              <Label htmlFor="and" className="text-sm cursor-pointer">
                AND (Tất cả điều kiện phải đúng)
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="OR" id="or" />
              <Label htmlFor="or" className="text-sm cursor-pointer">
                OR (Chỉ cần một điều kiện đúng)
              </Label>
            </div>
          </RadioGroup>
        </div>

        {/* Conditions */}
        <div className="space-y-3">
          <Label className="text-sm font-medium text-foreground">
            Điều kiện kiểm tra
          </Label>
          <div className="bg-muted/20 border border-border rounded-lg p-4">
            <ConditionsList
              conditions={conditions}
              logicOperator={logicOperator}
              onConditionChange={handleConditionChange}
              onRemoveCondition={handleRemoveCondition}
              onAddCondition={handleAddCondition}
              availableFields={availableFields}
              onAddCustomField={addCustomField}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4 border-t border-border">
          <Button
            onClick={handleSaveRule}
            disabled={
              !ruleName.trim() || conditions.every((c) => !c.value.trim())
            }
            className="flex items-center gap-2 px-4 py-2 h-auto font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4" />
            {isEditMode ? "Cập nhật quy tắc" : "Lưu quy tắc"}
          </Button>
          <Button
            variant="outline"
            onClick={handleCancel}
            className="px-4 py-2 h-auto border-border hover:bg-muted transition-all duration-200 font-medium"
          >
            Hủy
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
