import { useState } from "react";
import { useTaxRateConfig } from "@/hooks/useTaxRateConfig";
import { Card, CardContent } from "@/components/ui/card";
import {
  TaxRateConfigHeader,
  TaxRateConfigActions,
  TaxRateConfigForm,
} from "./taxRateConfig/index";

interface TaxRateConfigProps {
  className?: string;
}

export const TaxRateConfig = ({ className }: TaxRateConfigProps) => {
  const {
    config,
    isLoading,
    addRule,
    updateRule,
    removeRule,
    exportConfig,
    importConfig,
    resetToDefault,
    forceClearAndReset,
  } = useTaxRateConfig();

  const [importError, setImportError] = useState<string | null>(null);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Đang tải cấu hình...
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!config) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-destructive">
            Lỗi: Không thể tải cấu hình thuế suất
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <TaxRateConfigHeader />
      <CardContent className="space-y-4">
        <TaxRateConfigActions
          onExport={exportConfig}
          onImport={importConfig}
          onReset={resetToDefault}
          importError={importError}
          onImportErrorChange={setImportError}
        />

        <TaxRateConfigForm
          onAddRule={addRule}
          onUpdateRule={updateRule}
          existingRules={config.rules}
          onRemoveRule={removeRule}
        />

        {/* Debug Button - Remove in production */}
        <div className="border-t pt-4">
          <button
            onClick={forceClearAndReset}
            className="px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200"
          >
            Debug: Clear & Reset
          </button>
        </div>

        {/* Configuration Info */}
        <div className="text-xs text-muted-foreground">
          Cập nhật lần cuối:{" "}
          {new Date(config.lastUpdated).toLocaleString("vi-VN")}
        </div>
      </CardContent>
    </Card>
  );
};
