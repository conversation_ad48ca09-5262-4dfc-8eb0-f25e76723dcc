import React from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { InvoiceValidationResult } from "@/types/validation";
import {
  ErrorDisplay,
  ValidationHeader,
  ExtractedDataSummary,
  BuyerInfoComparison,
  ReferenceResults,
  TaxRateValidationResults,
} from "./validation/index";

interface ValidationViewerProps {
  validationResult: InvoiceValidationResult;
  selectedCompany?: string | null;
}

export const ValidationViewer: React.FC<ValidationViewerProps> = ({
  validationResult,
  selectedCompany,
}) => {
  const {
    fileName,
    extractedData,
    overallValid,
    errors,
    taxRateValidationResults,
  } = validationResult;

  if (errors.length > 0) {
    return <ErrorDisplay fileName={fileName} errors={errors} />;
  }

  return (
    <ScrollArea className="h-full w-full">
      <div className="p-4 space-y-4">
        {/* Header */}
        <ValidationHeader fileName={fileName} overallValid={overallValid} />

        {/* Extracted Data Summary */}
        <ExtractedDataSummary extractedData={extractedData} />

        {/* Buyer Information Comparison */}
        <BuyerInfoComparison
          extractedData={extractedData}
          selectedCompany={selectedCompany}
        />

        {/* Reference Results Summary */}
        <ReferenceResults
          extractedData={extractedData}
          selectedCompany={selectedCompany}
        />

        {/* Tax Rate Validation Results */}
        {taxRateValidationResults && (
          <TaxRateValidationResults
            taxRateValidationResults={taxRateValidationResults}
            extractedData={extractedData}
          />
        )}
      </div>
    </ScrollArea>
  );
};
