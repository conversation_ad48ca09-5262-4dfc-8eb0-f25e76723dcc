import { useRef } from 'react';
import { Upload, CheckCircle, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { useExcelParser } from '@/hooks/useExcelParser';
import { parseUnitConversionFile } from '@/utils/unitConversionUtils';
import type { ExcelParseOptions, ExcelParseResult } from '@/types/excel';
import type { UnitConversionResult, UnitConversionError } from '@/types/unitConversion';

export type ExcelInputType = 'excel' | 'unit-conversion';
export type ExcelInputResult = ExcelParseResult | UnitConversionResult;

export interface ExcelInputProps {
  /** Type of parsing to use */
  type: ExcelInputType;
  /** Callback when file is successfully parsed */
  onSuccess?: (result: any) => void;
  /** Callback when an error occurs */
  onError?: (error: string) => void;
  /** Custom parsing options (only for excel type) */
  options?: ExcelParseOptions;
  /** Callback when localStorage is updated (for unit conversion) */
  onLocalStorageUpdate?: () => void;
  /** Custom class name */
  className?: string;
}

/**
 * Simple reusable Excel input component
 * Can be used for both regular Excel parsing and unit conversion parsing
 */
export default function ExcelInput({
  type,
  onSuccess,
  onError,
  options,
  onLocalStorageUpdate,
  className = '',
}: ExcelInputProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    
    isUploading,
    isParsing,
    isSuccess,
    isError,
    result,
    error,
    progress,
    reset,
    handleInputChange,
  } = useExcelParser();

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  const handleReset = () => {
    reset();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleUnitConversionFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];

    try {
      const parseResult = await parseUnitConversionFile(file);
      onSuccess?.(parseResult);
      onLocalStorageUpdate?.();
    } catch (err) {
      const errorMessage = err instanceof Error
        ? err.message
        : (err as UnitConversionError).message || 'Lỗi không xác định';
      onError?.(errorMessage);
    }

    event.target.value = '';
  };

  const handleExcelFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const parseResult = await handleInputChange(event, options);
    if (parseResult) {
      onSuccess?.(parseResult);
    } else if (error) {
      onError?.(error.message);
    }
  };

  const isProcessing = isUploading || isParsing;
  const hasResult = isSuccess && result;
  const hasError = isError && error;

  return (
    <div className={`space-y-4 ${className}`}>
      {!hasResult && !hasError && (
        <>
          <Button
            onClick={handleButtonClick}
            disabled={isProcessing}
            className="w-full"
            variant="outline"
          >
            <Upload className="h-4 w-4 mr-2" />
            {isProcessing ? 'Đang xử lý...' : 'Chọn file Excel'}
          </Button>

          {isProcessing && (
            <div className="space-y-2">
              <Progress value={progress} className="w-full" />
              <div className="text-xs text-center text-muted-foreground">
                Đang xử lý file... {progress}%
              </div>
            </div>
          )}
        </>
      )}

      {/* Success state */}
      {hasResult && (
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-green-600">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">
              {type === 'unit-conversion' ? 'Import thành công!' : 'Phân tích thành công!'}
            </span>
          </div>
          
          <div className="text-sm text-muted-foreground space-y-1">
            <div>File: {result.filename}</div>
            <div>Số dòng dữ liệu: {result.dataRowCount}</div>
            {result.metadata?.parsedAt && (
              <div>Thời gian: {result.metadata.parsedAt.toLocaleString()}</div>
            )}
          </div>

          <Button
            onClick={handleReset}
            variant="outline"
            size="sm"
            className="w-full"
          >
            Tải file khác
          </Button>
        </div>
      )}

      {/* Error state */}
      {hasError && (
        <div className="space-y-3">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Lỗi import</span>
          </div>
          
          <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
            {error.message}
          </div>

          <Button
            onClick={handleReset}
            variant="outline"
            size="sm"
            className="w-full"
          >
            Thử lại
          </Button>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".xlsx,.xls,.xlsm"
        onChange={type === 'unit-conversion' ? handleUnitConversionFileChange : handleExcelFileChange}
        className="hidden"
      />
    </div>
  );
}
