import { useState } from 'react';
import { Download, Package } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { OrderSummaryResult } from '@/utils/orderSummaryUtils';
import { exportOrderSummaryToExcel } from '@/utils/orderSummaryUtils';

interface OrderSummaryTableProps {
  summary: OrderSummaryResult;
  onClose?: () => void;
  className?: string;
}

export default function OrderSummaryTable({
  summary,
  onClose,
  className = '',
}: OrderSummaryTableProps) {
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    setIsExporting(true);
    try {
      exportOrderSummaryToExcel(summary);
    } catch (error) {
      console.error('Export error:', error);
      alert('Lỗi khi xuất file Excel. Vui lòng thử lại.');
    } finally {
      setIsExporting(false);
    }
  };

  const formatNumber = (value: number): string => {
    return value % 1 === 0 ? value.toString() : value.toFixed(2);
  };

  if (summary.items.length === 0) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Tổng Hợp Đơn Hàng
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8 text-muted-foreground">
              <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">Không có dữ liệu đơn hàng</p>
              <p className="text-sm">
                Vui lòng kiểm tra dữ liệu Excel có đầy đủ các cột: Tên Vật Tư, DvT, Số Lượng
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Package className="h-6 w-6 text-primary" />
              </div>
              <div>
                <CardTitle className="flex items-center gap-2">
                  Tổng Hợp Đơn Hàng
                  <Badge variant="secondary">
                    {summary.totalItems} vật tư
                  </Badge>
                </CardTitle>
                <p className="text-sm text-muted-foreground mt-1">
                  Tổng hợp cho {summary.customers.length} khách hàng đã chọn • Sắp xếp theo tổng số lượng
                  {(summary as any).exportDate && (
                    <span className="text-primary font-medium"> • Lọc theo ngày xuất: {(summary as any).exportDate}</span>
                  )}
                </p>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                onClick={handleExport}
                disabled={isExporting}
                className="flex items-center gap-2"
              >
                {isExporting && (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Đang xuất...
                  </>
                )} 
                {!isExporting && (
                  <>
                    <Download className="h-4 w-4" />
                    Xuất Excel
                  </>
                )}
              </Button>
              
              {onClose && (
                <Button variant="outline" onClick={onClose}>
                  Đóng
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          <div className="overflow-auto h-[600px]">
            <div className="min-w-full">
              <Table className="min-w-max">
                <TableHeader className="sticky top-0 bg-background z-10">
                  <TableRow>
                    <TableHead className="min-w-[200px] font-semibold sticky left-0 bg-background z-20">
                      Tên Vật Tư
                    </TableHead>
                    <TableHead className="w-[100px] font-semibold">
                      DvT
                    </TableHead>
                    <TableHead className="w-[100px] font-semibold text-center">
                      Tổng
                    </TableHead>
                    <TableHead className="w-[100px] font-semibold text-center">
                      Hệ số
                    </TableHead>
                    {summary.customers.map((customer) => (
                      <TableHead
                        key={customer}
                        className="w-[120px] font-semibold text-center whitespace-nowrap"
                        title={customer}
                      >
                        <div className="truncate">
                          {customer}
                        </div>
                      </TableHead>
                    ))}
                    <TableHead className="w-[150px] font-semibold text-center">
                      Ngày đăng tải
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {summary.items.map((item, index) => (
                    <TableRow key={index} className="hover:bg-muted/50">
                      <TableCell className="font-medium sticky left-0 bg-background z-10">
                        <div className="max-w-[200px] truncate" title={item['Tên Vật Tư']}>
                          {item['Tên Vật Tư']}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="text-xs">
                          {item['DvT']}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-center font-semibold">
                        {formatNumber(item['Tổng'])}
                      </TableCell>
                      <TableCell className="text-center">
                        {item['Hệ số'] !== undefined && (
                          <span className="font-medium text-blue-600">
                            {formatNumber(item['Hệ số'] as number)}
                          </span>
                        )}
                      </TableCell>
                      {summary.customers.map((customer) => (
                        <TableCell key={customer} className="text-center whitespace-nowrap">
                          <span className={
                            (item[customer] as number) > 0
                              ? 'text-foreground font-medium'
                              : 'text-muted-foreground'
                          }>
                            {formatNumber(item[customer] as number)}
                          </span>
                        </TableCell>
                      ))}
                      <TableCell className="text-center whitespace-nowrap">
                        <span className="text-muted-foreground text-xs">
                          {summary.uploadDate ? new Date(summary.uploadDate).toLocaleDateString('vi-VN', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                          }) : ''}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-primary">
                {summary.totalItems}
              </div>
              <div className="text-sm text-muted-foreground">
                Loại vật tư
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">
                {summary.customers.length}
              </div>
              <div className="text-sm text-muted-foreground">
                Khách hàng
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">
                {formatNumber(
                  summary.items.reduce((sum, item) => sum + item['Tổng'], 0)
                )}
              </div>
              <div className="text-sm text-muted-foreground">
                Tổng số lượng
              </div>
            </div>
            <div>
              <div className="text-2xl font-bold text-primary">
                {new Set(summary.items.map(item => item['DvT'])).size}
              </div>
              <div className="text-sm text-muted-foreground">
                Đơn vị tính
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
