import { Checkbox } from '@/components/ui/checkbox';
import { Check, Minus } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import type { CustomerDocumentData } from '@/hooks/useCustomerDocumentExtraction';

interface CustomerTableProps {
  customers: string[];
  customerDocumentPairs?: CustomerDocumentData[];
  selectedCustomers: Set<string>;
  onCustomerToggle: (customer: string) => void;
  onCheckAll: () => void;
  allSelected: boolean;
  someSelected: boolean;
  searchTerm: string;
  showDocumentNumbers?: boolean;
  documentSearchTerm?: string;
  customerSearchTerm?: string;
  onDocumentSearchChange?: (value: string) => void;
  onCustomerSearchChange?: (value: string) => void;
}

// Simple native checkbox with custom styling
function SimpleCheckbox({
  checked,
  onCheckedChange,
  disabled = false,
  indeterminate = false
}: {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
  indeterminate?: boolean;
}) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onCheckedChange(e.target.checked);
  };

  return (
    <input
      type="checkbox"
      checked={checked}
      onChange={handleChange}
      disabled={disabled}
      ref={(el) => {
        if (el) el.indeterminate = indeterminate;
      }}
      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
    />
  );
}

export default function CustomerTable({
  customers,
  customerDocumentPairs = [],
  selectedCustomers,
  onCustomerToggle,
  onCheckAll,
  allSelected,
  someSelected,
  searchTerm,
  showDocumentNumbers = false,
  documentSearchTerm = '',
  customerSearchTerm = '',
  onDocumentSearchChange,
  onCustomerSearchChange,
}: CustomerTableProps) {
  // Determine what data to display
  const displayData = showDocumentNumbers && customerDocumentPairs.length > 0
    ? customerDocumentPairs
    : customers.map(customer => ({ customerName: customer, documentNumber: '' }));

  return (
    <div className="border rounded-lg overflow-hidden">
      <ScrollArea className="h-[400px]">
        <Table className="relative">
          <TableHeader className="sticky top-0 bg-background z-10">
            <TableRow>
              <TableHead className="w-12 text-center">
                <div className="flex items-center justify-center">
                  <SimpleCheckbox
                    checked={allSelected}
                    onCheckedChange={onCheckAll}
                    disabled={displayData.length === 0}
                    indeterminate={someSelected && !allSelected}
                  />
                </div>
              </TableHead>
              {showDocumentNumbers && customerDocumentPairs.length > 0 && (
                <TableHead className="font-semibold">Số chứng từ</TableHead>
              )}
              <TableHead className="font-semibold">Tên khách hàng</TableHead>
            </TableRow>
            {/* Search row */}
            <TableRow className="bg-muted/30">
              <TableHead className="w-12"></TableHead>
              {showDocumentNumbers && customerDocumentPairs.length > 0 && (
                <TableHead className="p-2">
                  <Input
                    type="text"
                    placeholder="Tìm kiếm số chứng từ..."
                    value={documentSearchTerm}
                    onChange={(e) => onDocumentSearchChange?.(e.target.value)}
                    className="h-8 text-sm"
                  />
                </TableHead>
              )}
              <TableHead className="p-2">
                <Input
                  type="text"
                  placeholder="Tìm kiếm tên khách hàng..."
                  value={customerSearchTerm}
                  onChange={(e) => onCustomerSearchChange?.(e.target.value)}
                  className="h-8 text-sm"
                />
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayData.length > 0 ? (
              displayData.map((item, index) => {
                const customer = item.customerName;
                const uniqueKey = showDocumentNumbers && customerDocumentPairs.length > 0
                  ? `${customer}-${item.documentNumber}-${index}`
                  : customer;

                return (
                  <TableRow key={uniqueKey} className="hover:bg-muted/50">
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center">
                        <SimpleCheckbox
                          checked={selectedCustomers.has(customer)}
                          onCheckedChange={() => onCustomerToggle(customer)}
                        />
                      </div>
                    </TableCell>
                    {showDocumentNumbers && customerDocumentPairs.length > 0 && (
                      <TableCell className="font-medium">
                        {item.documentNumber}
                      </TableCell>
                    )}
                    <TableCell className="font-medium">
                      {customer}
                    </TableCell>
                  </TableRow>
                );
              })
            ) : (
              <TableRow>
                <TableCell
                  colSpan={showDocumentNumbers && customerDocumentPairs.length > 0 ? 3 : 2}
                  className="text-center py-8 text-muted-foreground"
                >
                  {searchTerm ? 'Không tìm thấy khách hàng phù hợp' : 'Không có dữ liệu khách hàng'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </ScrollArea>
    </div>
  );
}
