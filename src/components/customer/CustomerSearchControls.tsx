import { CheckSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CustomerSearchControlsProps {
  onCheckAll: () => void;
  onClearAll: () => void;
  allSelected: boolean;
  selectedCount: number;
  filteredCount: number;
}

export default function CustomerSearchControls({
  onCheckAll,
  onClearAll,
  allSelected,
  selectedCount,
  filteredCount,
}: CustomerSearchControlsProps) {
  return (
    <div className="flex justify-end">
      {/* Action buttons - positioned at far right */}
      <div className="flex gap-2 items-center">
        {!allSelected && (
          <Button
            variant="outline"
            size="sm"
            onClick={onCheckAll}
            disabled={filteredCount === 0}
          >
            <CheckSquare className="h-4 w-4 mr-2" />
            Chọn tất cả
          </Button>
        )}

        {selectedCount > 0 && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClearAll}
          >
            Xóa tất cả
          </Button>
        )}
      </div>
    </div>
  );
}
