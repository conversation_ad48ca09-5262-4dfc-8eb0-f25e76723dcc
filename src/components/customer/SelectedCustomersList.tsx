import { UserCheck, Package } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

interface SelectedCustomersListProps {
  selectedCustomers: string[];
  onRemoveCustomer: (customer: string) => void;
  onOrderSummary?: () => void;
  exportDate?: string;
  orderSummaryError?: string;
}

export default function SelectedCustomersList({
  selectedCustomers,
  onRemoveCustomer,
  onOrderSummary,
  exportDate,
  orderSummaryError,
}: SelectedCustomersListProps) {
  if (selectedCustomers.length === 0) {
    return null;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserCheck className="h-5 w-5" />
          Khách hàng đã chọn
          <Badge variant="default">
            {selectedCustomers.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="max-h-[200px]">
          <div className="space-y-2">
            {selectedCustomers.map((customer) => (
              <div
                key={customer}
                className="flex items-center justify-between p-3 bg-muted/50 rounded-lg hover:bg-muted/70 transition-colors"
              >
                <span className="font-medium text-sm">{customer}</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onRemoveCustomer(customer)}
                  className="h-6 w-6 p-0 hover:bg-destructive/10 hover:text-destructive transition-colors"
                  title={`Xóa ${customer}`}
                >
                  <span className="text-lg leading-none">×</span>
                </Button>
              </div>
            ))}
          </div>
        </ScrollArea>

        {/* Order Summary Button */}
        {onOrderSummary && selectedCustomers.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <Button
              onClick={onOrderSummary}
              className="w-full"
              size="lg"
            >
              <Package className="h-4 w-4 mr-2" />
              Tổng Hợp Đơn Hàng
            </Button>

            {orderSummaryError && (
              <div className="text-xs text-red-600 text-center mt-2 p-2 bg-red-50 rounded">
                {orderSummaryError}
              </div>
            )}
            {!orderSummaryError && (
              <p className="text-xs text-muted-foreground text-center mt-2">
                Ngày xuất là tùy chọn - có thể tạo tổng hợp mà không cần chọn ngày
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
