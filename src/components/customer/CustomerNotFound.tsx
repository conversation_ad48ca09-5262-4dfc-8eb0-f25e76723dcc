import { Users } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { ExcelHeader } from '@/types/excel';

interface CustomerNotFoundProps {
  headers: ExcelHeader[];
}

export default function CustomerNotFound({ headers }: CustomerNotFoundProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Chọn khách hàng
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8 text-muted-foreground">
          <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p className="text-lg font-medium mb-2">Không tìm thấy cột khách hàng</p>
          <p className="text-sm mb-4">
            V<PERSON> lòng đảm bảo file Excel có cột I chứa tên khách hàng hoặc cột có tiêu đề chứa "khách hàng"
          </p>
          <div className="text-xs bg-muted p-3 rounded-lg">
            <p className="font-medium mb-2">Các cột có sẵn:</p>
            <div className="space-y-1 text-left max-h-32 overflow-y-auto">
              {headers.map(header => (
                <div key={header.index} className="flex justify-between">
                  <span className="font-mono">Cột {header.column}:</span>
                  <span className="ml-2">{header.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
