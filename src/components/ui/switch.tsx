"use client";

import * as React from "react";
import * as SwitchPrimitive from "@radix-ui/react-switch";

import { cn } from "@/lib/utils";

function Switch({
  className,
  ...props
}: React.ComponentProps<typeof SwitchPrimitive.Root>) {
  const { checked } = props;

  return (
    <SwitchPrimitive.Root
      className={cn(
        "peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full transition-all duration-200 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      style={{
        backgroundColor: checked ? "#22c55e" : "#9ca3af", // green-500 : gray-400
      }}
      {...props}
    >
      <SwitchPrimitive.Thumb
        className="pointer-events-none block h-4 w-4 rounded-full shadow-md transition-transform duration-200 ease-in-out"
        style={{
          backgroundColor: "#ffffff", // white
          transform: checked ? "translateX(16px)" : "translateX(-16px)",
        }}
      />
    </SwitchPrimitive.Root>
  );
}

export { Switch };
