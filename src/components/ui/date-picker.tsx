import * as React from "react";
import { cn } from "@/lib/utils";
import { Input } from "./input";

interface DatePickerProps {
  value?: string;
  onChange?: (date: string) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
}

export function DatePicker({
  value,
  onChange,
  placeholder = "Chọn ngày...",
  className,
  disabled = false,
}: DatePickerProps) {
  const formatDateForInput = (dateStr: string): string => {
    if (!dateStr) return "";
    const parts = dateStr.split("/");
    if (parts.length === 3) {
      const [day, month, year] = parts;
      return `${year}-${month.padStart(2, "0")}-${day.padStart(2, "0")}`;
    }
    return "";
  };

  // Convert YYYY-MM-DD to DD/MM/YYYY for display
  const formatDateForDisplay = (dateStr: string): string => {
    if (!dateStr) return "";
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return "";
    
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputDate = e.target.value;
    const displayDate = formatDateForDisplay(inputDate);
    onChange?.(displayDate);
  };

  const inputValue = formatDateForInput(value || "");

  return (
    <div className={cn("relative", className)}>
      <Input
        type="date"
        value={inputValue}
        onChange={handleDateChange}
        disabled={disabled}
        className=""
        placeholder={placeholder}
      />
    </div>
  );
}
