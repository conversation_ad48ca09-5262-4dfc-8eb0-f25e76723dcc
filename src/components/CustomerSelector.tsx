import { Users, Package } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { DatePicker } from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import type { ExcelParseResult } from '@/types/excel';

import CustomerSearchControls from '@/components/customer/CustomerSearchControls';
import CustomerTable from '@/components/customer/CustomerTable';
import SelectedCustomersList from '@/components/customer/SelectedCustomersList';
import CustomerNotFound from '@/components/customer/CustomerNotFound';

import { useCustomerDocumentExtraction } from '@/hooks/useCustomerDocumentExtraction';
import { useCustomerSelection } from '@/hooks/useCustomerSelection';
import { useCustomerDocumentFilter } from '@/hooks/useCustomerDocumentFilter';

interface CustomerSelectorProps {
  result: ExcelParseResult;
  onSelectionChange?: (selectedCustomers: string[]) => void;
  onOrderSummary?: (selectedCustomers: string[], exportDate?: string) => void;
  className?: string;
  exportDate?: string;
  onExportDateChange?: (date: string) => void;
  orderSummaryError?: string;
}

export default function CustomerSelector({
  result,
  onSelectionChange,
  onOrderSummary,
  className = '',
  exportDate,
  onExportDateChange,
  orderSummaryError,
}: CustomerSelectorProps) {
  const {
    uniqueCustomers,
    customerDocumentPairs,
    hasCustomerData,
    hasDocumentData
  } = useCustomerDocumentExtraction(result);

  const {
    documentSearchTerm,
    customerSearchTerm,
    setDocumentSearchTerm,
    setCustomerSearchTerm,
    filteredCustomers,
    filteredCustomerDocumentPairs,
    filteredCustomersFromPairs,
    hasFilter,
    filteredCount,
  } = useCustomerDocumentFilter(uniqueCustomers, customerDocumentPairs);

  const {
    selectedCustomers,
    selectedCount,
    toggleCustomer,
    toggleAllFiltered,
    selectAllFiltered,
    clearAll,
    getSelectionStats,
  } = useCustomerSelection(uniqueCustomers, onSelectionChange);

  const customersForSelection = hasFilter ? filteredCustomersFromPairs : filteredCustomers;
  const selectionStats = getSelectionStats(customersForSelection);

  if (!hasCustomerData) {
    return (
      <div className={`space-y-4 ${className}`}>
        <CustomerNotFound headers={result.headers} />
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Customer Selection Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Chọn khách hàng
            </CardTitle>
            <Badge variant="secondary">
              {uniqueCustomers.length} khách hàng
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Controls */}
          <CustomerSearchControls
            onCheckAll={() => selectAllFiltered(customersForSelection)}
            onClearAll={clearAll}
            allSelected={selectionStats.allSelected}
            selectedCount={selectedCount}
            filteredCount={filteredCount}
          />

          <CustomerTable
            customers={customersForSelection}
            customerDocumentPairs={filteredCustomerDocumentPairs}
            selectedCustomers={new Set(selectedCustomers)}
            onCustomerToggle={toggleCustomer}
            onCheckAll={() => toggleAllFiltered(customersForSelection)}
            allSelected={selectionStats.allSelected}
            someSelected={selectionStats.someSelected}
            searchTerm={documentSearchTerm || customerSearchTerm}
            showDocumentNumbers={hasDocumentData}
            documentSearchTerm={documentSearchTerm}
            customerSearchTerm={customerSearchTerm}
            onDocumentSearchChange={setDocumentSearchTerm}
            onCustomerSearchChange={setCustomerSearchTerm}
          />

          {hasFilter && (
            <div className="text-sm text-muted-foreground">
              Hiển thị {filteredCount} / {uniqueCustomers.length} khách hàng
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Chọn ngày xuất
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="export-date" className="text-sm font-medium">
              Ngày xuất (DD/MM/YYYY) - Tùy chọn
            </Label>
            <DatePicker
              value={exportDate}
              onChange={onExportDateChange}
              placeholder="Chọn ngày xuất (tùy chọn)..."
              className="w-48"
            />
            {exportDate && (
              <p className="text-xs text-muted-foreground">
                Ngày xuất: {exportDate}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      <SelectedCustomersList
        selectedCustomers={selectedCustomers}
        onRemoveCustomer={toggleCustomer}
        onOrderSummary={onOrderSummary ? () => onOrderSummary(selectedCustomers, exportDate) : undefined}
        exportDate={exportDate}
        orderSummaryError={orderSummaryError}
      />
    </div>
  );
}
