import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

export default function FileFormatRequirements() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Y<PERSON>u cầu định dạng file</CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid gap-4 sm:grid-cols-3">
          <div className="text-center p-4 bg-muted/50 rounded-lg">
            <div className="font-semibold text-lg text-primary">Dòng 8</div>
            <div className="text-sm text-muted-foreground">Tiêu đề cột</div>
          </div>
          <div className="text-center p-4 bg-muted/50 rounded-lg">
            <div className="font-semibold text-lg text-orange-600">Dòng 9</div>
            <div className="text-sm text-muted-foreground">Bỏ qua</div>
          </div>
          <div className="text-center p-4 bg-muted/50 rounded-lg">
            <div className="font-semibold text-lg text-green-600">Dòng 10+</div>
            <div className="text-sm text-muted-foreground">Dữ liệu</div>
          </div>
        </div>
        
        <Separator />
        
        <div className="space-y-2">
          <h4 className="font-medium">Lưu ý quan trọng:</h4>
          <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
            <li>File phải có định dạng .xlsx, .xls hoặc .xlsm</li>
            <li>Kích thước file không vượt quá 10MB</li>
            <li>Dòng 8 phải chứa tiêu đề cột (header)</li>
            <li>Dòng 9 sẽ được bỏ qua hoàn toàn</li>
            <li>Dữ liệu bắt đầu từ dòng 10 trở đi</li>
            <li><strong>Cột I:</strong> Tên khách hàng (để sử dụng tính năng chọn khách hàng)</li>
            <li>Các ô trống sẽ được hiển thị như chuỗi rỗng</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
