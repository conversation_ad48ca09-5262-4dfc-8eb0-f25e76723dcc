@import "tw-animate-css";

:root {
  font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
    Robot<PERSON>, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Enhanced radius system */
  --radius: 0.75rem;
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-xl: 1.5rem;

  /* Modern color palette - Light theme */
  --background: oklch(0.99 0.005 106.423);
  --foreground: oklch(0.15 0.02 258.338);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.15 0.02 258.338);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.15 0.02 258.338);

  /* Enhanced primary colors with better contrast */
  --primary: oklch(0.47 0.15 258.338);
  --primary-foreground: oklch(0.98 0.005 106.423);
  --primary-hover: oklch(0.42 0.15 258.338);

  /* Refined secondary colors */
  --secondary: oklch(0.96 0.01 258.338);
  --secondary-foreground: oklch(0.25 0.02 258.338);
  --secondary-hover: oklch(0.94 0.01 258.338);

  /* Better muted colors */
  --muted: oklch(0.96 0.01 258.338);
  --muted-foreground: oklch(0.55 0.02 258.338);

  /* Enhanced accent colors */
  --accent: oklch(0.94 0.02 258.338);
  --accent-foreground: oklch(0.25 0.02 258.338);
  --accent-hover: oklch(0.92 0.02 258.338);

  /* Improved destructive colors */
  --destructive: oklch(0.55 0.22 27.325);
  --destructive-foreground: oklch(0.98 0.005 106.423);

  /* Enhanced border and input colors */
  --border: oklch(0.92 0.01 258.338);
  --input: oklch(0.96 0.01 258.338);
  --ring: oklch(0.47 0.15 258.338);

  /* Chart colors */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  /* Enhanced sidebar colors */
  --sidebar: oklch(0.98 0.005 106.423);
  --sidebar-foreground: oklch(0.25 0.02 258.338);
  --sidebar-primary: oklch(0.47 0.15 258.338);
  --sidebar-primary-foreground: oklch(0.98 0.005 106.423);
  --sidebar-accent: oklch(0.94 0.02 258.338);
  --sidebar-accent-foreground: oklch(0.25 0.02 258.338);
  --sidebar-border: oklch(0.92 0.01 258.338);
  --sidebar-ring: oklch(0.47 0.15 258.338);

  /* New gradient variables */
  --gradient-primary: linear-gradient(
    135deg,
    oklch(0.47 0.15 258.338),
    oklch(0.52 0.18 280.338)
  );
  --gradient-secondary: linear-gradient(
    135deg,
    oklch(0.96 0.01 258.338),
    oklch(0.94 0.02 258.338)
  );

  /* Shadow system */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

/* Removed duplicate body styles - using the one below */

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

.dark {
  /* Enhanced dark theme colors */
  --background: oklch(0.08 0.02 258.338);
  --foreground: oklch(0.95 0.005 106.423);
  --card: oklch(0.12 0.02 258.338);
  --card-foreground: oklch(0.95 0.005 106.423);
  --popover: oklch(0.12 0.02 258.338);
  --popover-foreground: oklch(0.95 0.005 106.423);

  /* Enhanced primary colors for dark theme */
  --primary: oklch(0.65 0.18 258.338);
  --primary-foreground: oklch(0.08 0.02 258.338);
  --primary-hover: oklch(0.7 0.18 258.338);

  /* Refined secondary colors for dark theme */
  --secondary: oklch(0.18 0.02 258.338);
  --secondary-foreground: oklch(0.85 0.005 106.423);
  --secondary-hover: oklch(0.22 0.02 258.338);

  /* Better muted colors for dark theme */
  --muted: oklch(0.18 0.02 258.338);
  --muted-foreground: oklch(0.65 0.02 258.338);

  /* Enhanced accent colors for dark theme */
  --accent: oklch(0.22 0.02 258.338);
  --accent-foreground: oklch(0.85 0.005 106.423);
  --accent-hover: oklch(0.26 0.02 258.338);

  /* Improved destructive colors for dark theme */
  --destructive: oklch(0.65 0.22 27.325);
  --destructive-foreground: oklch(0.95 0.005 106.423);

  /* Enhanced border and input colors for dark theme */
  --border: oklch(0.25 0.02 258.338);
  --input: oklch(0.18 0.02 258.338);
  --ring: oklch(0.65 0.18 258.338);

  /* Chart colors for dark theme */
  --chart-1: oklch(0.55 0.25 264.376);
  --chart-2: oklch(0.7 0.17 162.48);
  --chart-3: oklch(0.77 0.19 70.08);
  --chart-4: oklch(0.63 0.27 303.9);
  --chart-5: oklch(0.65 0.25 16.439);

  /* Enhanced sidebar colors for dark theme */
  --sidebar: oklch(0.12 0.02 258.338);
  --sidebar-foreground: oklch(0.85 0.005 106.423);
  --sidebar-primary: oklch(0.65 0.18 258.338);
  --sidebar-primary-foreground: oklch(0.08 0.02 258.338);
  --sidebar-accent: oklch(0.22 0.02 258.338);
  --sidebar-accent-foreground: oklch(0.85 0.005 106.423);
  --sidebar-border: oklch(0.25 0.02 258.338);
  --sidebar-ring: oklch(0.65 0.18 258.338);

  /* Dark theme gradients */
  --gradient-primary: linear-gradient(
    135deg,
    oklch(0.65 0.18 258.338),
    oklch(0.7 0.2 280.338)
  );
  --gradient-secondary: linear-gradient(
    135deg,
    oklch(0.18 0.02 258.338),
    oklch(0.22 0.02 258.338)
  );

  /* Dark theme shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.4), 0 1px 2px -1px rgb(0 0 0 / 0.4);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4),
    0 4px 6px -4px rgb(0 0 0 / 0.4);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.4),
    0 8px 10px -6px rgb(0 0 0 / 0.4);
}

/* Base styles */
* {
  border-color: var(--border);
  outline-color: var(--ring);
  outline-width: 2px;
  outline-offset: 2px;
}

body {
  background-color: var(--background);
  color: var(--foreground);
  scroll-behavior: smooth;
  margin: 0;
  padding: 0;
  width: 100vw;
  min-height: 100vh;
}

/* Enhanced typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  letter-spacing: -0.025em;
}

h1 {
  font-size: 2.25rem;
  line-height: 1.1;
}

h2 {
  font-size: 1.875rem;
  line-height: 1.2;
}

h3 {
  font-size: 1.5rem;
  line-height: 1.3;
}

h4 {
  font-size: 1.25rem;
  line-height: 1.4;
}

@media (min-width: 1024px) {
  h1 {
    font-size: 3rem;
  }

  h2 {
    font-size: 2.25rem;
  }

  h3 {
    font-size: 1.875rem;
  }

  h4 {
    font-size: 1.5rem;
  }
}

/* Improved focus styles */
:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Better scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background-color: var(--muted);
}

::-webkit-scrollbar-thumb {
  background-color: var(--muted-foreground);
  opacity: 0.3;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb:hover {
  opacity: 0.5;
}

/* Component styles */

/* Enhanced card styles */
.card-enhanced {
  background-color: var(--card);
  color: var(--card-foreground);
  border-radius: 0.5rem;
  border: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
}

.card-enhanced:hover {
  box-shadow: var(--shadow-md);
}

/* Enhanced Select component styles */
[data-slot="select-trigger"] {
  background-color: var(--background);
  border: 1px solid var(--border);
  color: var(--foreground);
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  min-height: 2.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
}

[data-slot="select-trigger"]:hover {
  border-color: var(--muted-foreground);
  background-color: var(--muted);
}

[data-slot="select-trigger"]:focus {
  border-color: var(--foreground);
  box-shadow: 0 0 0 1px var(--muted-foreground);
  outline: none;
}

[data-slot="select-content"] {
  background-color: var(--background);
  border: 1px solid var(--border);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  z-index: 50;
}

[data-slot="select-item"] {
  color: var(--foreground);
  font-weight: 500;
  transition: all 0.15s ease-in-out;
  cursor: pointer;
}

[data-slot="select-item"]:hover {
  background-color: var(--muted);
  color: var(--foreground);
}

[data-slot="select-item"][data-state="checked"] {
  background-color: var(--muted);
  color: var(--foreground);
  font-weight: 600;
}

/* Gradient backgrounds */
.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

/* Button styles */
.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  white-space: nowrap;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  outline: none;
  border: 1px solid transparent;
  cursor: pointer;
}

.btn-base:disabled {
  pointer-events: none;
  opacity: 0.5;
}

.btn-base:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

.btn-base:active {
  transform: scale(0.98);
}

/* Button variants */
.btn-primary {
  background-color: var(--primary);
  color: var(--primary-foreground);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  box-shadow: var(--shadow-md);
  transform: scale(1.02);
}

.btn-destructive {
  background-color: var(--destructive);
  color: var(--destructive-foreground);
  box-shadow: var(--shadow-sm);
}

.btn-destructive:hover {
  opacity: 0.9;
  box-shadow: var(--shadow-md);
  transform: scale(1.02);
}

.btn-outline {
  border: 1px solid var(--border);
  background-color: var(--background);
  color: var(--foreground);
  box-shadow: var(--shadow-sm);
}

.btn-outline:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
  border-color: var(--primary);
  box-shadow: var(--shadow-md);
  transform: scale(1.02);
}

.btn-secondary {
  background-color: var(--secondary);
  color: var(--secondary-foreground);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background-color: var(--secondary-hover);
  box-shadow: var(--shadow-md);
  transform: scale(1.02);
}

.btn-ghost:hover {
  background-color: var(--accent);
  color: var(--accent-foreground);
  transform: scale(1.02);
}

.btn-link {
  color: var(--primary);
  text-decoration: underline;
  text-underline-offset: 4px;
}

.btn-link:hover {
  color: var(--primary-hover);
}

/* Button sizes */
.btn-default {
  height: 2.5rem;
  padding: 0.5rem 1rem;
}

.btn-sm {
  height: 2rem;
  border-radius: 0.375rem;
  gap: 0.375rem;
  padding: 0 0.75rem;
}

.btn-lg {
  height: 2.75rem;
  border-radius: 0.375rem;
  padding: 0 1.5rem;
}

.btn-icon {
  height: 2.5rem;
  width: 2.5rem;
  padding: 0;
}

/* Mobile-first responsive utilities */
.container-responsive {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.container-responsive-limited {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
  max-width: 1280px;
}

@media (min-width: 640px) {
  .container-responsive {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .container-responsive-limited {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .container-responsive-limited {
    padding-left: 2rem;
    padding-right: 2rem;
  }
  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
}

/* Responsive text sizes */
.text-responsive-sm {
  font-size: 0.875rem;
}

@media (min-width: 640px) {
  .text-responsive-sm {
    font-size: 1rem;
  }
}

.text-responsive-md {
  font-size: 1rem;
}

@media (min-width: 640px) {
  .text-responsive-md {
    font-size: 1.125rem;
  }
}

.text-responsive-lg {
  font-size: 1.125rem;
}

@media (min-width: 640px) {
  .text-responsive-lg {
    font-size: 1.25rem;
  }
}

@media (min-width: 1024px) {
  .text-responsive-lg {
    font-size: 1.5rem;
  }
}

.text-responsive-xl {
  font-size: 1.25rem;
}

@media (min-width: 640px) {
  .text-responsive-xl {
    font-size: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .text-responsive-xl {
    font-size: 1.875rem;
  }
}

/* Utility Classes */

/* Layout */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.flex-1 {
  flex: 1 1 0%;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: flex-start;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.space-y-1 > * + * {
  margin-top: 0.25rem;
}
.space-y-2 > * + * {
  margin-top: 0.5rem;
}
.space-y-4 > * + * {
  margin-top: 1rem;
}

/* Sizing */
.h-full {
  height: 100%;
}
.h-screen {
  height: 100vh;
}
.min-h-screen {
  min-height: 100vh;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.w-full {
  width: 100%;
}
.w-screen {
  width: 100vw;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-80 {
  width: 20rem;
}
.min-h-0 {
  min-height: 0px;
}
.min-w-0 {
  min-width: 0px;
}

/* Spacing */
.p-0 {
  padding: 0;
}
.p-2 {
  padding: 0.5rem;
}
.p-4 {
  padding: 1rem;
}
.p-6 {
  padding: 1.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.py-10 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-8 {
  padding-top: 2rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.mr-2 {
  margin-right: 0.5rem;
}

/* Text */
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-xs {
  font-size: 0.75rem;
}
.text-sm {
  font-size: 0.875rem;
}
.text-base {
  font-size: 1rem;
}
.text-lg {
  font-size: 1.125rem;
}
.text-xl {
  font-size: 1.25rem;
}
.text-2xl {
  font-size: 1.5rem;
}
.text-3xl {
  font-size: 1.875rem;
}
.text-4xl {
  font-size: 2.25rem;
}
.text-5xl {
  font-size: 3rem;
}
.text-6xl {
  font-size: 3.75rem;
}
.font-medium {
  font-weight: 500;
}
.font-bold {
  font-weight: 700;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Colors */
.text-foreground {
  color: var(--foreground);
}
.text-muted-foreground {
  color: var(--muted-foreground);
}
.text-sidebar-foreground {
  color: var(--sidebar-foreground);
}
.text-destructive {
  color: var(--destructive);
}
.bg-background {
  background-color: var(--background);
}
.bg-card {
  background-color: var(--card);
}
.bg-sidebar {
  background-color: var(--sidebar);
}
.bg-accent {
  background-color: var(--accent);
}
.bg-primary\/5 {
  background-color: color-mix(in srgb, var(--primary) 5%, transparent);
}
.bg-accent\/30 {
  background-color: color-mix(in srgb, var(--accent) 30%, transparent);
}
.bg-accent\/50 {
  background-color: color-mix(in srgb, var(--accent) 50%, transparent);
}
.bg-muted\/30 {
  background-color: color-mix(in srgb, var(--muted) 30%, transparent);
}
.bg-transparent {
  background-color: transparent;
}

/* Borders */
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0;
}
.border-2 {
  border-width: 2px;
}
.border-r {
  border-right-width: 1px;
}
.border-dashed {
  border-style: dashed;
}
.border-border {
  border-color: var(--border);
}
.border-sidebar-border {
  border-color: var(--sidebar-border);
}
.border-primary {
  border-color: var(--primary);
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-full {
  border-radius: 9999px;
}

/* Shadows */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}
.shadow-md {
  box-shadow: var(--shadow-md);
}
.shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* Positioning */
.relative {
  position: relative;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-hidden {
  overflow-x: hidden;
}
.overflow-y-auto {
  overflow-y: auto;
}

/* Additional layout classes */
.border-b {
  border-bottom-width: 1px;
}
.size-icon {
  height: 2.5rem;
  width: 2.5rem;
}

/* Interactions */
.cursor-pointer {
  cursor: pointer;
}
.transition-all {
  transition: all 0.15s ease-in-out;
}
.transition-colors {
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out,
    border-color 0.15s ease-in-out;
}
.transition-transform {
  transition: transform 0.15s ease-in-out;
}
.duration-150 {
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.transform {
  transform: translateZ(0);
}
.scale-110 {
  transform: scale(1.1);
}
.scale-\[1\.02\] {
  transform: scale(1.02);
}

/* Hover states */
.hover\:bg-sidebar-accent:hover {
  background-color: var(--sidebar-accent);
}
.hover\:text-sidebar-accent-foreground:hover {
  color: var(--sidebar-accent-foreground);
}
.hover\:bg-accent\/30:hover {
  background-color: color-mix(in srgb, var(--accent) 30%, transparent);
}
.hover\:border-primary\/50:hover {
  border-color: color-mix(in srgb, var(--primary) 50%, transparent);
}

/* Additional sizing classes */
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}

/* Additional responsive classes */
@media (min-width: 768px) {
  .md\:block {
    display: block;
  }
  .md\:flex {
    display: flex;
  }
  .md\:min-w-0 {
    min-width: 0px;
  }
}

/* Animations */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Additional utility classes */
.hidden {
  display: none;
}
.block {
  display: block;
}
.inline {
  display: inline;
}
.inline-flex {
  display: inline-flex;
}
.table-cell {
  display: table-cell;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.whitespace-pre-wrap {
  white-space: pre-wrap;
}
.break-all {
  word-break: break-all;
}
.leading-relaxed {
  line-height: 1.625;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas,
    "Liberation Mono", Menlo, monospace;
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Responsive utilities */
@media (min-width: 640px) {
  .sm\:flex-row {
    flex-direction: row;
  }
  .sm\:items-center {
    align-items: center;
  }
  .sm\:justify-between {
    justify-content: space-between;
  }
  .sm\:text-base {
    font-size: 1rem;
  }
  .sm\:text-lg {
    font-size: 1.125rem;
  }
  .sm\:text-xl {
    font-size: 1.25rem;
  }
  .sm\:text-2xl {
    font-size: 1.5rem;
  }
  .sm\:text-6xl {
    font-size: 3.75rem;
  }
  .sm\:py-8 {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  .sm\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }
  .sm\:pt-8 {
    padding-top: 2rem;
  }
  .sm\:p-6 {
    padding: 1.5rem;
  }
  .sm\:block {
    display: block;
  }
  .sm\:hidden {
    display: none;
  }
  .sm\:inline {
    display: inline;
  }
}

@media (min-width: 768px) {
  .md\:block {
    display: block;
  }
  .md\:flex {
    display: flex;
  }
  .md\:min-w-0 {
    min-width: 0px;
  }
}

@media (min-width: 1024px) {
  .lg\:block {
    display: block;
  }
  .lg\:hidden {
    display: none;
  }
  .lg\:table-cell {
    display: table-cell;
  }
  .lg\:text-2xl {
    font-size: 1.5rem;
  }
  .lg\:text-3xl {
    font-size: 1.875rem;
  }
  .lg\:text-4xl {
    font-size: 2.25rem;
  }
  .lg\:text-5xl {
    font-size: 3rem;
  }
}

/* Additional spacing */
.space-x-0 > * + * {
  margin-left: 0;
}
.max-w-xs {
  max-width: 20rem;
}
.min-h-\[300px\] {
  min-height: 300px;
}
.min-h-\[44px\] {
  min-height: 44px;
}
.min-w-\[44px\] {
  min-width: 44px;
}

/* Table utilities */
.w-\[50px\] {
  width: 50px;
}
.w-\[100px\] {
  width: 100px;
}

/* Flex utilities */
.flex-shrink-0 {
  flex-shrink: 0;
}
.items-stretch {
  align-items: stretch;
}

/* Additional colors */
.text-blue-500 {
  color: #3b82f6;
}
.text-orange-500 {
  color: #f97316;
}
.text-gray-500 {
  color: #6b7280;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .dark\:bg-slate-950 {
    background-color: #020617;
  }
  .dark\:border-slate-700 {
    border-color: #334155;
  }
  .dark\:text-slate-400 {
    color: #94a3b8;
  }
  .dark\:bg-slate-800 {
    background-color: #1e293b;
  }
  .dark\:bg-slate-800\/50 {
    background-color: color-mix(in srgb, #1e293b 50%, transparent);
  }
}

/* Ensure full width for all containers */
html,
body,
#root {
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden !important;
  box-sizing: border-box;
}

/* Box sizing for all elements */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* Fix for resizable panels */
[data-panel-group] {
  width: 100% !important;
  height: 100% !important;
}

[data-panel] {
  width: 100% !important;
  height: 100% !important;
}

[data-panel-resize-handle] {
  background-color: var(--border);
}

[data-panel-resize-handle]:hover {
  background-color: var(--primary);
}

/* Checkbox specific styles */
[data-radix-checkbox-root] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  border-radius: 0.125rem;
  border: 1px solid var(--border);
  background-color: var(--background);
  transition: all 0.2s ease-in-out;
}

[data-radix-checkbox-root]:hover {
  border-color: var(--primary);
}

[data-radix-checkbox-root]:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

[data-radix-checkbox-root][data-state="checked"] {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--primary-foreground);
}

[data-radix-checkbox-root][data-state="indeterminate"] {
  background-color: var(--primary);
  border-color: var(--primary);
  color: var(--primary-foreground);
}

[data-radix-checkbox-indicator] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Force checkbox styling */
.checkbox-custom {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 1rem;
  height: 1rem;
  border: 1px solid var(--border);
  border-radius: 0.125rem;
  background-color: var(--background);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.checkbox-custom:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.checkbox-custom:checked::after {
  content: "✓";
  color: var(--primary-foreground);
  font-size: 0.75rem;
  font-weight: bold;
}
