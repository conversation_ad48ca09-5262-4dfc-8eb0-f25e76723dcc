import { BrowserRouter, Routes, Route } from "react-router-dom";
import MainLayout from "@/layouts/MainLayout";
import PVPage from "@/pages/PVPage";
import InvoicePage from "@/pages/InvoicePage";

function App() {
  return (
    <BrowserRouter>
      <div className="w-screen min-h-screen overflow-x-hidden">
        <Routes>
          <Route path="/" element={<MainLayout />} />
          <Route path="/pv" element={<PVPage />} />
          <Route path="/invoice" element={<InvoicePage />} />
        </Routes>
      </div>
    </BrowserRouter>
  );
}

export default App;
