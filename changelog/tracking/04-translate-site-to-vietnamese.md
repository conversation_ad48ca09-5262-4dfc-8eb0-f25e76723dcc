# Convert Site to Vietnamese (Vietnamese Only)

## Overview
This task involves converting all user-facing text in the application to Vietnamese. The site will only support Vietnamese.

## Plan
- [x] **Identify User-Facing Strings**:
    - [x] Scan the codebase (primarily `.tsx` files in `src/components`, `src/pages`, `src/layouts`) for all user-facing English strings.
- [x] **Obtain Vietnamese Translations**:
    - [x] User provided Vietnamese translations.
- [x] **Replace Strings in Components**:
    - [x] Directly replaced English strings with Vietnamese translations in relevant `.tsx` files.
- [x] **Testing & Verification**:
    - [ ] Manually review the site to ensure all text is in Vietnamese and displays correctly. (User to verify)
    - [ ] Check for any layout issues caused by text length changes. (User to verify)
    - [x] Run `pnpm build` to ensure the application builds successfully.
- [x] **Documentation**:
    - [x] Reviewed `README.md` files and updated where necessary to reflect Vietnamese UI.
- [x] **Changelog**:
    - [x] Appended changes to `changelog/2025/06-1.md`.
- [x] **Commit**:
    - [x] Provided Git commit message.

## Key Files to Modify (Initial List - may expand)
- `src/components/**/*.tsx` (for text replacement)
- `src/pages/**/*.tsx` (for text replacement)
- `src/layouts/**/*.tsx` (for text replacement)

## Notes:
- No i18n library will be used as the site is Vietnamese-only.
- All English text will be directly replaced with Vietnamese.
- User will provide the Vietnamese translations.
