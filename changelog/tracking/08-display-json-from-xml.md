# Plan: Display JSON Data Converted from XML

## Overview
Change the XML processing to display the JSON representation of the XML data instead of the flattened columnar format. This will provide a more hierarchical and structured view of the XML content.

## Checklist
- [x] 1. **Update XML Processing Logic**:
  - [x] 1.1. Modify `src/utils/xmlUtils.ts` to provide a function that converts XML to JSON object instead of flattened rows.
  - [x] 1.2. Update `src/types/fileSystem.ts` to store JSON data instead of the flattened array format.
- [x] 2. **Update File Processing**:
  - [x] 2.1. Modify `src/utils/fileSystemUtils.ts` to use the new JSON conversion function.
- [x] 3. **Create JSON Viewer Component**:
  - [x] 3.1. Create `src/components/folderDropPage/JsonViewer.tsx` to display JSON data in a readable, expandable format.
  - [x] 3.2. Used `<pre>` tag with `JSON.stringify` for basic display. (Syntax highlighting deferred).
- [x] 4. **Update UI Components**:
  - [x] 4.1. Replace `XmlDataViewer` usage in `src/pages/FolderDropPage.tsx` with the new `JsonViewer`.
  - [x] 4.2. Update references from `xmlData` to `jsonData` in relevant components.
  - [x] 4.3. Deleted unused `XmlDataViewer.tsx` component.
- [x] 5. **Verify Changes**:
  - [x] 5.1. Test with various XML files to ensure JSON conversion works correctly.
  - [x] 5.2. Run linters and build the project.
- [x] 6. **Update Documentation**:
  - [x] 6.1. Update `changelog/2025/06-1.md` with the change from columnar to JSON display.
- [x] 7. **Prepare Git Commit Message**.
