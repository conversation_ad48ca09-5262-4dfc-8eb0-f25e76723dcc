# Plan: Fix Table Hydration Error

## Overview
Address the React hydration error: "In HTML, whitespace text nodes cannot be a child of `<table>`." This error occurs when rendering the `XmlDataViewer` component, likely related to the `Table` component from `shadcn/ui`.

## Checklist
- [x] 1. Read the content of `src/components/ui/table.tsx` to understand its implementation.
- [x] 2. Analyze the `Table` component's structure and the usage in `XmlDataViewer.tsx` to identify the source of the invalid whitespace.
- [x] 3. Modify `src/components/ui/table.tsx` to filter out non-element children (whitespace text nodes) that could cause the hydration error.
- [x] 4. Verify the fix by running the application and testing the XML viewing functionality.
- [x] 5. Run linters and build the project to ensure no new issues are introduced.
- [ ] 6. Update `changelog/2025/06-1.md` with a summary of the fix.
- [ ] 7. Prepare a Git commit message.
