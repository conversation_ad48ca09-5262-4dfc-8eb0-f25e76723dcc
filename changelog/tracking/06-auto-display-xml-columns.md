# Plan: Auto-Display Parsed XML Data in a Separate Panel

## Overview
This plan outlines the steps to refactor the XML data display. Instead of an expandable section within the file table, parsed XML data for a selected XML file will automatically appear in a dedicated panel to the right of the file list.

## Checklist
- [x] 1. **Modify Page Layout (`FolderDropPage.tsx`)**:
  - [x] 1.1. Implement a two-panel layout using `ResizablePanelGroup` and `ResizablePanel` from `shadcn/ui`.
    - Left panel: Will contain the `FileDisplayTable`.
    - Right panel: Will display the parsed XML data of the selected file.
  - [x] 1.2. Add state to `FolderDropPage.tsx` to store the currently selected `FileEntry` (e.g., `selectedFileForXmlView: FileEntry | null`).
- [x] 2. **Update `FileDisplayTable.tsx`**:
  - [x] 2.1. Remove the existing XML expansion logic (state for `expandedXmlFile`, "Actions" column, and the expandable `TableRow` with `XmlDataViewer`).
  - [x] 2.2. Add a new prop `onFileSelect: (file: FileEntry) => void`.
  - [x] 2.3. Add a new prop `selectedFilePath?: string | null` to highlight the selected row.
  - [x] 2.4. Make rows clickable. When a row representing an XML file is clicked, call `onFileSelect` with that file's data. For non-XML files, clicking might clear the selection or do nothing specific for XML view.
  - [x] 2.5. Apply a visual style (e.g., background color) to the currently selected row if its path matches `selectedFilePath`.
- [x] 3. **Adapt `XmlDataViewer.tsx` (or create `SelectedXmlDetailsPanel.tsx`)**:
  - [x] 3.1. This component will now be used in the right panel of `FolderDropPage.tsx`.
  - [x] 3.2. It will receive the `xmlData` and `fileName` (or the entire `FileEntry`) of the `selectedFileForXmlView` as props.
  - [x] 3.3. If no file is selected, or the selected file is not an XML file / has no `xmlData`, it should display a placeholder message (e.g., "Select an XML file to view its details").
- [x] 4. **Integrate in `FolderDropPage.tsx`**:
  - [x] 4.1. Pass the `onFileSelect` handler to `FileDisplayTable`. This handler will update the `selectedFileForXmlView` state.
  - [x] 4.2. Pass `selectedFileForXmlView?.path` to `FileDisplayTable` as `selectedFilePath`.
  - [x] 4.3. Render the `XmlDataViewer` (or the new panel component) in the right resizable panel, passing it the `xmlData` and `name` from `selectedFileForXmlView` if it's an XML file.
- [x] 5. **Refine Styling and User Experience**: (Initial styling and UX implemented)
  - [x] 5.1. Ensure the resizable panels work smoothly.
  - [x] 5.2. Ensure clear visual indication of the selected XML file in the list.
- [x] 6. **Verify Changes**:
  - [x] 6.1. Test with various XML files and non-XML files.
  - [x] 6.2. Check behavior when selecting different files.
  - [x] 6.3. Run linters and build the project.
- [ ] 7. **Update Changelog (`changelog/2025/06-1.md`)**:
  - [ ] 7.1. Document the new XML display mechanism.
- [ ] 8. **Prepare Git Commit Message**.
