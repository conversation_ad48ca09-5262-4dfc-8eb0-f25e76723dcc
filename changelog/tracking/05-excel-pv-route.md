# Task 05: Create /pv Route for Excel File Upload and Parsing

## Overview
Create a new route `/pv` that allows users to select and parse Excel files with specific parsing rules:
- Row 8: Header row
- Row 9: Skip this row
- Row 10+: Data rows

## Detailed Plan

### Phase 1: Setup Routing Infrastructure
- [x] Install React Router DOM for client-side routing
- [x] Update App.tsx to implement routing structure
- [x] Modify MainLayout to support different pages based on routes
- [x] Update SideNav to include navigation to /pv route

### Phase 2: Install Excel Processing Dependencies
- [x] Install xlsx library for Excel file parsing
- [x] Install file upload related dependencies if needed
- [x] Add TypeScript types for Excel data structures

### Phase 3: Create PV Page Components
- [x] Create `src/pages/PVPage.tsx` - Main page for /pv route
- [x] Create `src/components/ExcelUpload.tsx` - File upload component
- [x] Create `src/components/ExcelDataTable.tsx` - Display parsed data
- [x] Create `src/hooks/useExcelParser.ts` - Custom hook for Excel parsing logic

### Phase 4: Implement Excel File Processing
- [x] Create utility functions in `src/utils/excelUtils.ts`:
  - File validation (ensure it's Excel format)
  - Parse Excel file starting from row 8 as header
  - Skip row 9
  - Extract data from row 10 onwards
  - Handle errors and edge cases

### Phase 5: Create User Interface
- [x] Design file upload area with drag & drop support
- [x] Create data preview table with proper styling
- [x] Add loading states and error handling
- [x] Implement responsive design for mobile/desktop

### Phase 6: Integration and Testing
- [x] Test file upload functionality
- [x] Test Excel parsing with various file formats
- [x] Verify routing works correctly
- [x] Test responsive design
- [x] Add proper error messages and validation

### Phase 7: Documentation
- [x] Update README.md with new /pv route information
- [x] Document Excel parsing requirements
- [x] Add usage examples

## Technical Requirements

### Dependencies to Install
- `react-router-dom` - For routing
- `xlsx` - For Excel file parsing
- `@types/xlsx` - TypeScript types for xlsx

### File Structure Changes
```
src/
├── pages/
│   ├── FolderDropPage.tsx (existing)
│   └── PVPage.tsx (new)
├── components/
│   ├── ExcelUpload.tsx (new)
│   └── ExcelDataTable.tsx (new)
├── hooks/
│   └── useExcelParser.ts (new)
├── utils/
│   └── excelUtils.ts (new)
└── types/
    └── excel.ts (new)
```

### Excel Parsing Specification
- **Row 8**: Header row - extract column names
- **Row 9**: Skip completely
- **Row 10+**: Data rows - parse according to header structure
- **Error Handling**: Invalid files, missing rows, parsing errors
- **Supported Formats**: .xlsx, .xls files

## Success Criteria
- [x] User can navigate to /pv route
- [x] User can upload Excel files via drag & drop or file picker
- [x] Excel files are parsed correctly according to specifications
- [x] Parsed data is displayed in a clean, readable table format
- [x] Error handling works for invalid files or parsing issues
- [x] Responsive design works on mobile and desktop
- [x] Navigation between routes works seamlessly

## Implementation Complete ✅

All phases have been successfully implemented. The /pv route is now fully functional with:

1. **Routing Infrastructure**: React Router DOM integrated with proper navigation
2. **Excel Processing**: Full Excel parsing with row 8 headers, skip row 9, data from row 10+
3. **User Interface**: Modern, responsive design with drag & drop file upload
4. **Error Handling**: Comprehensive validation and error messages
5. **Data Display**: Interactive table with search, pagination, and export functionality

The application is ready for use and testing!
