# Plan: Integrate XML Processing and Display

## Overview
This plan outlines the steps to integrate XML file processing into the `FolderDropPage`. When an XML file is loaded, its content will be parsed and displayed in a columnar format.

## Checklist
- [x] 1. Install `fast-xml-parser` dependency.
- [x] 2. Create `src/utils/xmlUtils.ts` and add the provided `xmlToRows` function.
- [x] 3. Update `src/types/fileSystem.ts`:
  - [x] 3.1. Add an optional `xmlData?: Record<string, string>[]` field to the `FileEntry` interface to store parsed XML.
  - [x] 3.2. Add an optional `rawContent?: string` field to `FileEntry` for XML file content.
- [x] 4. Modify `src/hooks/useFolderDrop.ts`: (Achieved by modifying `src/utils/fileSystemUtils.ts` which is used by the hook)
  - [x] 4.1. When processing files (both drop and select), if a file is an XML file (e.g., ends with `.xml`):
    - [x] 4.1.1. Read its content as a string.
    - [x] 4.1.2. Parse it using `xmlToRows` from `xmlUtils.ts`.
    - [x] 4.1.3. Store the raw content and parsed data in the `FileEntry` object.
- [x] 5. Modify `src/components/folderDropPage/FileDisplayTable.tsx`:
  - [x] 5.1. If a `FileEntry` has `xmlData`, display a button or icon to view/expand XML content.
  - [x] 5.2. Implement a way to display the XML data (as an expandable section with a sub-table).
  - [x] 5.3. Adjust the table to include an "Actions" column for the expansion button and used a specific icon for XML files.
- [x] 6. Update `src/pages/FolderDropPage.tsx` if necessary to pass any new props or handle new states. (No changes needed).
- [x] 7. Verify the changes:
  - [x] 7.1. Test with sample XML files.
  - [x] 7.2. Run linters and build the project.
- [x] 8. Update `changelog/2025/06-1.md` with a summary of the new XML processing feature.
- [x] 9. Prepare a Git commit message.
